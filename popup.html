<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书签检测器</title>
    <link rel="stylesheet" href="styles/popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">书签检测器</h1>
            <div class="controls">
                <button id="scanBtn" class="btn btn-primary">
                    <span class="icon">🔍</span>
                    开始扫描
                </button>
                <button id="settingsBtn" class="btn btn-secondary">
                    <span class="icon">⚙️</span>
                    设置
                </button>
            </div>
        </header>

        <div class="stats">
            <div class="stat-item">
                <span class="stat-label">总书签:</span>
                <span id="totalCount" class="stat-value">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">有效:</span>
                <span id="validCount" class="stat-value valid">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">失效:</span>
                <span id="invalidCount" class="stat-value invalid">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">检测中:</span>
                <span id="checkingCount" class="stat-value checking">0</span>
            </div>
        </div>

        <div class="filters">
            <button class="filter-btn active" data-filter="all">全部</button>
            <button class="filter-btn" data-filter="valid">有效</button>
            <button class="filter-btn" data-filter="invalid">失效</button>
            <button class="filter-btn" data-filter="unchecked">未检测</button>
        </div>

        <div class="search-box">
            <input type="text" id="searchInput" placeholder="搜索书签..." class="search-input">
        </div>

        <div class="bookmark-list" id="bookmarkList">
            <div class="loading" id="loadingIndicator">
                <div class="spinner"></div>
                <span>加载书签中...</span>
            </div>
        </div>

        <div class="batch-actions" id="batchActions" style="display: none;">
            <div class="batch-info">
                <span id="selectedCount">0</span> 个书签已选择
            </div>
            <div class="batch-buttons">
                <button id="selectAllBtn" class="btn btn-secondary">全选</button>
                <button id="deselectAllBtn" class="btn btn-secondary">取消选择</button>
                <button id="deleteSelectedBtn" class="btn btn-danger">删除选中</button>
            </div>
        </div>

        <div class="actions">
            <button id="deleteInvalidBtn" class="btn btn-danger" disabled>
                删除失效书签
            </button>
            <button id="exportBtn" class="btn btn-secondary">
                导出报告
            </button>
            <button id="toggleSelectBtn" class="btn btn-secondary">
                批量选择
            </button>
        </div>
    </div>

    <script src="scripts/popup.js"></script>
</body>
</html>
