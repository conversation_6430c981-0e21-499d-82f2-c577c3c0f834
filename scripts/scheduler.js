/**
 * 定时检测管理器
 * 负责管理书签的定时自动检测功能
 */
/* global BookmarkChecker */
class ScheduledChecker {
  constructor() {
    this.alarmName = 'bookmarkAutoCheck';
    this.intervals = {
      daily: { minutes: 24 * 60, label: '每日' },
      weekly: { minutes: 7 * 24 * 60, label: '每周' },
      monthly: { minutes: 30 * 24 * 60, label: '每月' },
      custom: { minutes: 0, label: '自定义' },
    };
    this.defaultSettings = {
      enabled: false,
      interval: 'weekly',
      customMinutes: 60,
      lastRun: null,
      nextRun: null,
      onlyInvalid: false, // 是否只检测失效的书签
      maxConcurrent: 5, // 最大并发检测数
    };
  }

  /**
   * 初始化定时检测器
   */
  async init() {
    // 监听alarm事件
    chrome.alarms.onAlarm.addListener((alarm) => {
      if (alarm.name === this.alarmName) {
        this.executeScheduledCheck();
      }
    });

    // 恢复已设置的定时器
    await this.restoreSchedule();
  }

  /**
   * 获取当前设置
   */
  async getSettings() {
    const result = await chrome.storage.local.get(['scheduledCheck']);
    return { ...this.defaultSettings, ...result.scheduledCheck };
  }

  /**
   * 保存设置
   */
  async saveSettings(settings) {
    await chrome.storage.local.set({
      scheduledCheck: { ...this.defaultSettings, ...settings },
    });
  }

  /**
   * 启用定时检测
   */
  async enableSchedule(interval = 'weekly', customMinutes = 60) {
    const settings = await this.getSettings();

    // 清除现有的alarm
    await chrome.alarms.clear(this.alarmName);

    // 计算检测间隔
    let delayInMinutes;
    if (interval === 'custom') {
      delayInMinutes = customMinutes;
    } else {
      delayInMinutes = this.intervals[interval].minutes;
    }

    // 创建新的alarm
    await chrome.alarms.create(this.alarmName, {
      delayInMinutes: delayInMinutes,
      periodInMinutes: delayInMinutes,
    });

    // 保存设置
    const nextRun = Date.now() + delayInMinutes * 60 * 1000;
    await this.saveSettings({
      ...settings,
      enabled: true,
      interval: interval,
      customMinutes: customMinutes,
      nextRun: nextRun,
    });

    console.log(`定时检测已启用，间隔: ${delayInMinutes} 分钟`);
    return true;
  }

  /**
   * 禁用定时检测
   */
  async disableSchedule() {
    await chrome.alarms.clear(this.alarmName);

    const settings = await this.getSettings();
    await this.saveSettings({
      ...settings,
      enabled: false,
      nextRun: null,
    });

    console.log('定时检测已禁用');
    return true;
  }

  /**
   * 恢复定时器设置
   */
  async restoreSchedule() {
    const settings = await this.getSettings();

    if (settings.enabled) {
      // 检查是否已有alarm
      const alarm = await chrome.alarms.get(this.alarmName);

      if (!alarm) {
        // 重新创建alarm
        await this.enableSchedule(settings.interval, settings.customMinutes);
      }
    }
  }

  /**
   * 执行定时检测
   */
  async executeScheduledCheck() {
    console.log('开始执行定时检测...');

    const settings = await this.getSettings();

    try {
      // 更新最后运行时间
      await this.saveSettings({
        ...settings,
        lastRun: Date.now(),
      });

      // 获取书签检测器实例
      const checker = new BookmarkChecker();
      await checker.init();

      // 获取需要检测的书签
      let bookmarksToCheck;
      if (settings.onlyInvalid) {
        // 只检测失效的书签
        bookmarksToCheck = await this.getInvalidBookmarks(checker);
      } else {
        // 检测所有书签
        bookmarksToCheck = await checker.getAllBookmarks();
      }

      console.log(`定时检测: 准备检测 ${bookmarksToCheck.length} 个书签`);

      // 执行检测
      await this.performBatchCheck(
        checker,
        bookmarksToCheck,
        settings.maxConcurrent,
      );

      // 发送完成通知
      await this.sendCompletionNotification(bookmarksToCheck.length);
    } catch (error) {
      console.error('定时检测执行失败:', error);

      // 发送错误通知
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: '定时检测失败',
        message: `检测过程中发生错误: ${error.message}`,
      });
    }
  }

  /**
   * 获取失效的书签
   */
  async getInvalidBookmarks(checker) {
    const allBookmarks = await checker.getAllBookmarks();
    const results = new Map();

    // 加载已有的检测结果
    const stored = await chrome.storage.local.get(['bookmarkResults']);
    if (stored.bookmarkResults) {
      for (const [id, result] of Object.entries(stored.bookmarkResults)) {
        results.set(id, result);
      }
    }

    // 筛选失效的书签
    return allBookmarks.filter((bookmark) => {
      const result = results.get(bookmark.id);
      return (
        result && (result.status === 'invalid' || result.status === 'error')
      );
    });
  }

  /**
   * 执行批量检测
   */
  async performBatchCheck(checker, bookmarks, maxConcurrent) {
    const results = new Map();
    const batches = this.createBatches(bookmarks, maxConcurrent);

    for (const batch of batches) {
      const promises = batch.map(async(bookmark) => {
        try {
          const result = await checker.checkBookmark(bookmark.url);
          results.set(bookmark.id, {
            ...result,
            checkedAt: Date.now(),
            scheduledCheck: true,
          });
        } catch (error) {
          results.set(bookmark.id, {
            status: 'error',
            error: error.message,
            checkedAt: Date.now(),
            scheduledCheck: true,
          });
        }
      });

      await Promise.all(promises);

      // 批次间短暂延迟，避免过载
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    // 保存结果
    await this.saveResults(results);
    return results;
  }

  /**
   * 创建检测批次
   */
  createBatches(items, batchSize) {
    const batches = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 保存检测结果
   */
  async saveResults(results) {
    const stored = await chrome.storage.local.get(['bookmarkResults']);
    const existingResults = stored.bookmarkResults || {};

    // 合并结果
    for (const [id, result] of results.entries()) {
      existingResults[id] = result;
    }

    await chrome.storage.local.set({ bookmarkResults: existingResults });
  }

  /**
   * 发送完成通知
   */
  async sendCompletionNotification(totalChecked) {
    const settings = await chrome.storage.local.get(['settings']);
    if (!settings.settings?.showNotifications) {
      return;
    }

    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: '定时检测完成',
      message: `已完成 ${totalChecked} 个书签的检测`,
    });
  }

  /**
   * 获取下次运行时间
   */
  async getNextRunTime() {
    const settings = await this.getSettings();
    return settings.nextRun;
  }

  /**
   * 获取定时检测状态
   */
  async getStatus() {
    const settings = await this.getSettings();
    const alarm = await chrome.alarms.get(this.alarmName);

    return {
      enabled: settings.enabled,
      interval: settings.interval,
      customMinutes: settings.customMinutes,
      lastRun: settings.lastRun,
      nextRun: alarm ? alarm.scheduledTime : null,
      isActive: !!alarm,
    };
  }

  /**
   * 立即执行一次检测
   */
  async runNow() {
    await this.executeScheduledCheck();
  }
}

// 全局实例
self.scheduledChecker = new ScheduledChecker();
