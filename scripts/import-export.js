/**
 * 书签导入导出管理器
 * 支持多种格式的书签导入和导出功能
 */
class ImportExportManager {
  constructor() {
    this.supportedFormats = {
      import: ['json', 'html'],
      export: ['json', 'html', 'csv'],
    };
  }

  /**
   * 导入书签
   */
  async importBookmarks(file, format = null) {
    try {
      // 自动检测格式
      if (!format) {
        format = this.detectFormat(file);
      }

      const content = await this.readFile(file);
      let bookmarks = [];

      switch (format.toLowerCase()) {
        case 'json':
          bookmarks = await this.parseJsonBookmarks(content);
          break;
        case 'html':
          bookmarks = await this.parseHtmlBookmarks(content);
          break;
        default:
          throw new Error(`不支持的导入格式: ${format}`);
      }

      // 验证书签数据
      const validBookmarks = this.validateBookmarks(bookmarks);

      // 导入到Chrome书签
      const importResult = await this.importToChrome(validBookmarks);

      return {
        success: true,
        imported: importResult.imported,
        skipped: importResult.skipped,
        errors: importResult.errors,
        total: validBookmarks.length,
      };
    } catch (error) {
      console.error('导入书签失败:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 导出书签
   */
  async exportBookmarks(format, options = {}) {
    try {
      // 获取所有书签
      const bookmarks = await this.getAllBookmarks();

      // 获取检测结果
      const results = await this.getBookmarkResults();

      // 合并书签和检测结果
      const enrichedBookmarks = this.enrichBookmarksWithResults(
        bookmarks,
        results,
      );

      let content = '';
      let filename = '';
      let mimeType = '';

      switch (format.toLowerCase()) {
        case 'json':
          content = this.exportToJson(enrichedBookmarks, options);
          filename = `bookmarks-${this.getTimestamp()}.json`;
          mimeType = 'application/json';
          break;
        case 'html':
          content = this.exportToHtml(enrichedBookmarks, options);
          filename = `bookmarks-${this.getTimestamp()}.html`;
          mimeType = 'text/html';
          break;
        case 'csv':
          content = this.exportToCsv(enrichedBookmarks, options);
          filename = `bookmarks-${this.getTimestamp()}.csv`;
          mimeType = 'text/csv';
          break;
        default:
          throw new Error(`不支持的导出格式: ${format}`);
      }

      // 下载文件
      this.downloadFile(content, filename, mimeType);

      return {
        success: true,
        filename: filename,
        count: enrichedBookmarks.length,
      };
    } catch (error) {
      console.error('导出书签失败:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 检测文件格式
   */
  detectFormat(file) {
    const extension = file.name.split('.').pop().toLowerCase();

    if (extension === 'json') {
      return 'json';
    } else if (extension === 'html' || extension === 'htm') {
      return 'html';
    }

    // 默认尝试JSON
    return 'json';
  }

  /**
   * 读取文件内容
   */
  async readFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsText(file);
    });
  }

  /**
   * 解析JSON格式的书签
   */
  async parseJsonBookmarks(content) {
    try {
      const data = JSON.parse(content);

      // 支持多种JSON格式
      if (Array.isArray(data)) {
        return data;
      } else if (data.bookmarks && Array.isArray(data.bookmarks)) {
        return data.bookmarks;
      } else if (data.roots) {
        // Chrome书签导出格式
        return this.extractFromChromeFormat(data);
      }

      throw new Error('无法识别的JSON书签格式');
    } catch (error) {
      throw new Error(`JSON解析失败: ${error.message}`);
    }
  }

  /**
   * 解析HTML格式的书签
   */
  async parseHtmlBookmarks(content) {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(content, 'text/html');
      const links = doc.querySelectorAll('a[href]');

      const bookmarks = [];

      links.forEach((link) => {
        const url = link.getAttribute('href');
        const title = link.textContent.trim() || url;

        if (url && this.isValidUrl(url)) {
          bookmarks.push({
            title: title,
            url: url,
            dateAdded: Date.now(),
          });
        }
      });

      return bookmarks;
    } catch (error) {
      throw new Error(`HTML解析失败: ${error.message}`);
    }
  }

  /**
   * 从Chrome格式中提取书签
   */
  extractFromChromeFormat(data) {
    const bookmarks = [];

    const traverse = (node) => {
      if (node.type === 'url' && node.url) {
        bookmarks.push({
          title: node.name || node.url,
          url: node.url,
          dateAdded: node.date_added ? parseInt(node.date_added) : Date.now(),
        });
      } else if (node.children) {
        node.children.forEach(traverse);
      }
    };

    if (data.roots) {
      Object.values(data.roots).forEach(traverse);
    }

    return bookmarks;
  }

  /**
   * 验证书签数据
   */
  validateBookmarks(bookmarks) {
    return bookmarks.filter((bookmark) => {
      return (
        bookmark &&
        typeof bookmark === 'object' &&
        bookmark.url &&
        this.isValidUrl(bookmark.url) &&
        bookmark.title
      );
    });
  }

  /**
   * 验证URL格式
   */
  isValidUrl(url) {
    try {
      const urlObj = new URL(url);
      return ['http:', 'https:'].includes(urlObj.protocol);
    } catch {
      return false;
    }
  }

  /**
   * 导入到Chrome书签
   */
  async importToChrome(bookmarks) {
    const result = {
      imported: 0,
      skipped: 0,
      errors: [],
    };

    // 创建导入文件夹
    const importFolder = await chrome.bookmarks.create({
      parentId: '1', // 书签栏
      title: `导入书签 ${new Date().toLocaleString()}`,
    });

    for (const bookmark of bookmarks) {
      try {
        // 检查是否已存在相同URL的书签
        const existing = await this.findExistingBookmark(bookmark.url);

        if (existing) {
          result.skipped++;
          continue;
        }

        // 创建书签
        await chrome.bookmarks.create({
          parentId: importFolder.id,
          title: bookmark.title,
          url: bookmark.url,
        });

        result.imported++;
      } catch (error) {
        result.errors.push({
          bookmark: bookmark,
          error: error.message,
        });
      }
    }

    return result;
  }

  /**
   * 查找已存在的书签
   */
  async findExistingBookmark(url) {
    try {
      const results = await chrome.bookmarks.search({ url: url });
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取所有书签
   */
  async getAllBookmarks() {
    const bookmarkTree = await chrome.bookmarks.getTree();
    const bookmarks = [];

    const traverse = (nodes) => {
      for (const node of nodes) {
        if (node.url) {
          bookmarks.push({
            id: node.id,
            title: node.title,
            url: node.url,
            parentId: node.parentId,
            dateAdded: node.dateAdded,
          });
        }
        if (node.children) {
          traverse(node.children);
        }
      }
    };

    traverse(bookmarkTree);
    return bookmarks;
  }

  /**
   * 获取书签检测结果
   */
  async getBookmarkResults() {
    try {
      const stored = await chrome.storage.local.get(['bookmarkResults']);
      return stored.bookmarkResults || {};
    } catch (error) {
      return {};
    }
  }

  /**
   * 合并书签和检测结果
   */
  enrichBookmarksWithResults(bookmarks, results) {
    return bookmarks.map((bookmark) => {
      const result = results[bookmark.id];
      return {
        ...bookmark,
        status: result ? result.status : 'unchecked',
        lastChecked: result ? result.lastChecked : null,
        error: result ? result.error : null,
        responseTime: result ? result.responseTime : null,
      };
    });
  }

  /**
   * 导出为JSON格式
   */
  exportToJson(bookmarks) {
    const data = {
      exportDate: new Date().toISOString(),
      version: '1.0',
      source: 'Bookmark Checker Extension',
      total: bookmarks.length,
      bookmarks: bookmarks,
    };

    return JSON.stringify(data, null, 2);
  }

  /**
   * 导出为HTML格式
   */
  exportToHtml(bookmarks) {
    const html = `<!DOCTYPE NETSCAPE-Bookmark-file-1>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">
<TITLE>Bookmarks</TITLE>
<H1>Bookmarks</H1>
<DL><p>
${bookmarks
    .map(
      (bookmark) =>
        `    <DT><A HREF="${bookmark.url}" ADD_DATE="${Math.floor(
          (bookmark.dateAdded || Date.now()) / 1000,
        )}">${bookmark.title}</A>`,
    )
    .join('\n')}
</DL><p>`;

    return html;
  }

  /**
   * 导出为CSV格式
   */
  exportToCsv(bookmarks) {
    const headers = ['标题', 'URL', '状态', '最后检测', '响应时间', '错误信息'];
    const rows = [headers];

    bookmarks.forEach((bookmark) => {
      rows.push([
        bookmark.title || '',
        bookmark.url || '',
        bookmark.status || 'unchecked',
        bookmark.lastChecked
          ? new Date(bookmark.lastChecked).toLocaleString()
          : '',
        bookmark.responseTime ? `${bookmark.responseTime}ms` : '',
        bookmark.error || '',
      ]);
    });

    return rows
      .map((row) => row.map((cell) => `"${cell}"`).join(','))
      .join('\n');
  }

  /**
   * 下载文件
   */
  downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.style.display = 'none';

    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    URL.revokeObjectURL(url);
  }

  /**
   * 获取时间戳字符串
   */
  getTimestamp() {
    return new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
  }
}

// 全局实例
window.importExportManager = new ImportExportManager();
