// Popup脚本 - 处理用户界面交互

class BookmarkPanel {
  constructor() {
    this.bookmarks = [];
    this.results = new Map();
    this.currentFilter = 'all';
    this.searchTerm = '';
    this.isChecking = false;
    this.selectedBookmarks = new Set();
    this.batchMode = false;
    this.redirectSuggestions = [];

    // UI更新优化
    this.updateQueue = new Set();
    this.updateTimer = null;
    this.batchUpdateDelay = 100; // 100ms批量更新延迟

    this.init();
  }

  async init() {
    this.bindEvents();
    await this.loadBookmarks();
    await this.loadResults();
    await this.loadRedirectSuggestions();
    this.updateUI();

    // 监听来自background的消息
    chrome.runtime.onMessage.addListener((message) => {
      this.handleBackgroundMessage(message);
    });
  }

  bindEvents() {
    // 扫描按钮
    document.getElementById('scanBtn').addEventListener('click', () => {
      this.startScanning();
    });

    // 设置按钮
    document.getElementById('settingsBtn').addEventListener('click', () => {
      this.openSettings();
    });

    // 过滤按钮
    document.querySelectorAll('.filter-btn').forEach((btn) => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const filter = e.target.dataset.filter;
        console.log('Filter button clicked:', filter);
        this.setFilter(filter);
      });
    });

    // 搜索框
    document.getElementById('searchInput').addEventListener('input', (e) => {
      this.searchTerm = e.target.value.toLowerCase();
      this.updateBookmarkList();
    });

    // 删除失效书签按钮
    document
      .getElementById('deleteInvalidBtn')
      .addEventListener('click', () => {
        this.deleteInvalidBookmarks();
      });

    // 导出报告按钮
    document.getElementById('exportBtn').addEventListener('click', () => {
      this.exportReport();
    });

    // 批量选择相关按钮
    document.getElementById('toggleSelectBtn').addEventListener('click', () => {
      this.toggleBatchMode();
    });

    document.getElementById('selectAllBtn').addEventListener('click', () => {
      this.selectAll();
    });

    document.getElementById('deselectAllBtn').addEventListener('click', () => {
      this.deselectAll();
    });

    document
      .getElementById('deleteSelectedBtn')
      .addEventListener('click', () => {
        this.deleteSelected();
      });
  }

  async loadBookmarks() {
    try {
      // 检查扩展是否正常运行
      if (!chrome.runtime || !chrome.runtime.sendMessage) {
        throw new Error('Chrome runtime not available');
      }

      console.log('Attempting to load bookmarks...');
      const response = await chrome.runtime.sendMessage({
        action: 'getBookmarks',
      });

      if (response && response.success) {
        this.bookmarks = response.data;
        console.log(`Loaded ${this.bookmarks.length} bookmarks`);
      } else {
        const errorMsg = response
          ? response.error
          : 'No response from background script';
        console.error('Failed to load bookmarks:', errorMsg);
        this.showError('加载书签失败: ' + errorMsg);
      }
    } catch (error) {
      console.error('Error loading bookmarks:', error);

      // 提供更详细的错误信息
      let errorMessage = '加载书签时发生错误';
      if (error.message.includes('Could not establish connection')) {
        errorMessage = '无法连接到后台脚本，请尝试重新加载扩展';
      } else if (error.message.includes('Extension context invalidated')) {
        errorMessage = '扩展上下文已失效，请重新加载扩展';
      }

      this.showError(errorMessage);

      // 显示重新加载按钮
      this.showReloadButton();
    }
  }

  async loadResults() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'getResults',
      });

      if (response.success) {
        this.results = new Map(response.data);
        console.log(`Loaded ${this.results.size} results`);
      }
    } catch (error) {
      console.error('Error loading results:', error);
    }
  }

  async loadRedirectSuggestions() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'getRedirectSuggestions',
      });

      if (response.success) {
        this.redirectSuggestions = response.data;
        console.log(
          `Loaded ${this.redirectSuggestions.length} redirect suggestions`
        );
      }
    } catch (error) {
      console.error('Error loading redirect suggestions:', error);
    }
  }

  handleBackgroundMessage(message) {
    switch (message.type) {
      case 'bookmarkChecked':
        this.results.set(message.data.id, message.data.result);
        // 使用批量更新而不是立即更新
        this.queueUIUpdate(message.data.id);
        this.updateProgress();
        break;

      case 'checkingComplete':
        this.isChecking = false;
        this.updateScanButton();
        this.hideProgress();
        this.showSuccess('书签检测完成');
        // 强制最终更新
        this.forceUIUpdate();
        break;

      case 'checkingStarted':
        this.isChecking = true;
        this.updateScanButton();
        this.showProgress();
        break;
    }
  }

  async startScanning(useIncremental = false) {
    if (this.isChecking) {
      return;
    }

    this.isChecking = true;
    this.updateScanButton();

    // 清除旧结果
    this.results.clear();

    try {
      let bookmarksToCheck = this.bookmarks;

      if (useIncremental) {
        // 使用智能增量检测
        const response = await chrome.runtime.sendMessage({
          action: 'getSmartCheckList',
          options: {
            maxBookmarks: 50,
            includeInvalid: true,
            prioritizeRecent: true,
          },
        });

        if (response.success) {
          bookmarksToCheck = response.data;
          console.log(`增量检测: 将检测 ${bookmarksToCheck.length} 个书签`);

          // 显示增量检测提示
          this.showIncrementalInfo(
            bookmarksToCheck.length,
            this.bookmarks.length
          );
        }
      }

      await chrome.runtime.sendMessage({
        action: 'checkBookmarks',
        bookmarks: bookmarksToCheck,
      });
    } catch (error) {
      console.error('Error starting scan:', error);
      this.isChecking = false;
      this.updateScanButton();
    }
  }

  setFilter(filter) {
    console.log('Setting filter to:', filter);
    this.currentFilter = filter;

    // 更新按钮状态
    document.querySelectorAll('.filter-btn').forEach((btn) => {
      btn.classList.toggle('active', btn.dataset.filter === filter);
    });

    this.updateBookmarkList();
  }

  // 队列UI更新，避免频繁刷新
  queueUIUpdate(bookmarkId) {
    this.updateQueue.add(bookmarkId);

    // 清除之前的定时器
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
    }

    // 设置新的批量更新定时器
    this.updateTimer = setTimeout(() => {
      this.processBatchUpdate();
    }, this.batchUpdateDelay);
  }

  // 强制立即更新UI
  forceUIUpdate() {
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = null;
    }
    this.updateQueue.clear();
    this.updateUI();
  }

  // 处理批量更新
  processBatchUpdate() {
    if (this.updateQueue.size === 0) {
      return;
    }

    // 只更新统计信息，不重新渲染整个列表
    this.updateStats();

    // 更新特定的书签项目
    this.updateQueue.forEach((bookmarkId) => {
      this.updateBookmarkItem(bookmarkId);
    });

    this.updateActionButtons();
    this.updateQueue.clear();
    this.updateTimer = null;
  }

  updateUI() {
    this.updateStats();
    this.updateBookmarkList();
    this.updateActionButtons();
    this.updateRedirectSuggestions();
  }

  updateStats() {
    const total = this.bookmarks.length;
    let valid = 0,
      invalid = 0,
      checking = 0,
      unchecked = 0;

    for (const bookmark of this.bookmarks) {
      const result = this.results.get(bookmark.id);
      if (!result) {
        unchecked++;
        continue;
      }

      switch (result.status) {
        case 'valid':
          valid++;
          break;
        case 'invalid':
        case 'error':
          invalid++;
          break;
        case 'warning':
          // 警告状态算作有效但需要注意
          valid++;
          break;
        case 'checking':
          checking++;
          break;
      }
    }

    // 如果正在检测，计算检测中的数量
    if (this.isChecking) {
      checking = total - valid - invalid - unchecked;
      if (checking < 0) {
        checking = 0;
      }
    }

    // 安全更新统计数据
    const totalCountEl = document.getElementById('totalCount');
    const validCountEl = document.getElementById('validCount');
    const invalidCountEl = document.getElementById('invalidCount');
    const checkingCountEl = document.getElementById('checkingCount');

    if (totalCountEl) {
      totalCountEl.textContent = total;
    }
    if (validCountEl) {
      validCountEl.textContent = valid;
    }
    if (invalidCountEl) {
      invalidCountEl.textContent = invalid;
    }
    if (checkingCountEl) {
      checkingCountEl.textContent = checking;
    }

    // 更新筛选按钮的计数
    this.updateFilterCounts(total, valid, invalid, unchecked);
  }

  updateFilterCounts(total, valid, invalid, unchecked) {
    const allCountEl = document.getElementById('allCount');
    const validFilterCountEl = document.getElementById('validFilterCount');
    const invalidFilterCountEl = document.getElementById('invalidFilterCount');
    const uncheckedCountEl = document.getElementById('uncheckedCount');

    if (allCountEl) {
      allCountEl.textContent = `(${total})`;
    }
    if (validFilterCountEl) {
      validFilterCountEl.textContent = `(${valid})`;
    }
    if (invalidFilterCountEl) {
      invalidFilterCountEl.textContent = `(${invalid})`;
    }
    if (uncheckedCountEl) {
      uncheckedCountEl.textContent = `(${unchecked})`;
    }
  }

  // 更新单个书签项目，避免重新渲染整个列表
  updateBookmarkItem(bookmarkId) {
    const bookmarkElement = document.querySelector(`[data-id="${bookmarkId}"]`);
    if (!bookmarkElement) {
      return; // 如果元素不在当前视图中，跳过更新
    }

    const bookmark = this.bookmarks.find((b) => b.id === bookmarkId);
    if (!bookmark) {
      return;
    }

    const result = this.results.get(bookmarkId);
    if (!result) {
      return;
    }

    // 更新状态指示器
    const statusElement = bookmarkElement.querySelector('.bookmark-status');
    if (statusElement) {
      statusElement.className = `bookmark-status ${result.status}`;

      // 更新状态提示信息
      let statusInfo = '';
      let methodInfo = '';
      if (result.statusCode) {
        statusInfo += ` (${result.statusCode})`;
      }
      if (result.responseTime) {
        statusInfo += ` - ${result.responseTime}ms`;
      }
      if (result.method) {
        methodInfo = ` [${result.method}]`;
      }
      if (result.note) {
        statusInfo += ` - ${result.note}`;
      }
      statusElement.title = `${result.status}${statusInfo}${methodInfo}`;
    }

    // 更新错误信息
    const existingError = bookmarkElement.querySelector('.bookmark-error');
    if (result.error) {
      if (!existingError) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'bookmark-error';
        errorDiv.textContent = result.error;
        errorDiv.title = result.error;
        bookmarkElement.appendChild(errorDiv);
      } else {
        existingError.textContent = result.error;
        existingError.title = result.error;
      }
    } else if (existingError) {
      existingError.remove();
    }
  }

  updateBookmarkList() {
    const container = document.getElementById('bookmarkList');
    const loading = document.getElementById('loadingIndicator');

    if (this.bookmarks.length === 0) {
      if (loading) {
        loading.style.display = 'flex';
      }
      return;
    }

    if (loading) {
      loading.style.display = 'none';
    }

    const filteredBookmarks = this.getFilteredBookmarks();

    if (filteredBookmarks.length === 0) {
      container.innerHTML = `
                <div class="empty-state">
                    <div class="icon">📭</div>
                    <div>没有找到匹配的书签</div>
                </div>
            `;
      return;
    }

    container.innerHTML = filteredBookmarks
      .map((bookmark) => this.createBookmarkItem(bookmark))
      .join('');

    // 绑定书签项目事件
    this.bindBookmarkEvents();
  }

  getFilteredBookmarks() {
    return this.bookmarks.filter((bookmark) => {
      // 搜索过滤
      if (this.searchTerm) {
        const searchMatch =
          bookmark.title.toLowerCase().includes(this.searchTerm) ||
          bookmark.url.toLowerCase().includes(this.searchTerm);
        if (!searchMatch) {
          return false;
        }
      }

      // 状态过滤
      if (this.currentFilter === 'all') {
        return true;
      }

      const result = this.results.get(bookmark.id);

      switch (this.currentFilter) {
        case 'valid':
          return (
            result && (result.status === 'valid' || result.status === 'warning')
          );
        case 'invalid':
          return (
            result && (result.status === 'invalid' || result.status === 'error')
          );
        case 'unchecked':
          return !result;
        default:
          return true;
      }
    });
  }

  createBookmarkItem(bookmark) {
    const result = this.results.get(bookmark.id);
    const status = result ? result.status : 'unchecked';
    const error = result && result.error ? result.error : '';
    const responseTime =
      result && result.responseTime ? result.responseTime : null;
    const statusCode = result && result.statusCode ? result.statusCode : null;

    // 转义HTML特殊字符
    const escapeHtml = (text) => {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    };

    const safeTitle = escapeHtml(bookmark.title || 'Untitled');
    const safeUrl = escapeHtml(bookmark.url);
    const safeError = error ? escapeHtml(error) : '';

    let statusInfo = '';
    let methodInfo = '';
    if (result) {
      if (statusCode) {
        statusInfo += ` (${statusCode})`;
      }
      if (responseTime) {
        statusInfo += ` - ${responseTime}ms`;
      }
      if (result.method) {
        methodInfo = ` [${result.method}]`;
      }
      if (result.note) {
        statusInfo += ` - ${result.note}`;
      }
    }

    const isSelected = this.selectedBookmarks.has(bookmark.id);
    const checkboxHtml = this.batchMode
      ? `<input type="checkbox" class="bookmark-checkbox" ${
          isSelected ? 'checked' : ''
        } data-bookmark-id="${bookmark.id}">`
      : '';

    return `
            <div class="bookmark-item ${
              isSelected ? 'selected' : ''
            }" data-id="${bookmark.id}">
                <div class="bookmark-header">
                    ${checkboxHtml}
                    <div class="bookmark-status ${status}" title="${status}${statusInfo}${methodInfo}"></div>
                    <div class="bookmark-title" title="${safeTitle}">${safeTitle}</div>
                    <div class="bookmark-actions">
                        <button class="action-btn" data-action="open" title="打开链接">
                            <span class="material-icons">open_in_new</span>
                        </button>
                        <button class="action-btn" data-action="check" title="重新检测">
                            <span class="material-icons">refresh</span>
                        </button>
                        <button class="action-btn" data-action="delete" title="删除书签">
                            <span class="material-icons">delete</span>
                        </button>
                    </div>
                </div>
                <div class="bookmark-url" title="${safeUrl}">${safeUrl}</div>
                ${
                  error
                    ? `<div class="bookmark-error" title="${safeError}">${safeError}</div>`
                    : ''
                }
            </div>
        `;
  }

  bindBookmarkEvents() {
    document.querySelectorAll('.action-btn').forEach((btn) => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();

        // 获取action，可能在按钮本身或其父元素上
        let actionElement = e.target;
        if (!actionElement.dataset.action) {
          actionElement = e.target.closest('.action-btn');
        }

        const action = actionElement.dataset.action;
        const bookmarkId = actionElement.closest('.bookmark-item').dataset.id;
        this.handleBookmarkAction(action, bookmarkId);
      });
    });

    // 绑定复选框事件
    document.querySelectorAll('.bookmark-checkbox').forEach((checkbox) => {
      checkbox.addEventListener('change', (e) => {
        const bookmarkId = e.target.dataset.bookmarkId;
        if (e.target.checked) {
          this.selectedBookmarks.add(bookmarkId);
        } else {
          this.selectedBookmarks.delete(bookmarkId);
        }
        this.updateSelectedCount();

        // 更新书签项的选中状态
        const bookmarkItem = e.target.closest('.bookmark-item');
        bookmarkItem.classList.toggle('selected', e.target.checked);
      });
    });
  }

  async handleBookmarkAction(action, bookmarkId) {
    console.log('Bookmark action triggered:', action, bookmarkId);

    const bookmark = this.bookmarks.find((b) => b.id === bookmarkId);
    if (!bookmark) {
      console.error('Bookmark not found:', bookmarkId);
      return;
    }

    switch (action) {
      case 'open':
        console.log('Opening bookmark:', bookmark.url);
        chrome.tabs.create({ url: bookmark.url });
        break;

      case 'check':
        console.log('Checking bookmark:', bookmark.title);
        await this.checkSingleBookmark(bookmark);
        break;

      case 'delete':
        if (confirm(`确定要删除书签 "${bookmark.title}" 吗？`)) {
          console.log('Deleting bookmark:', bookmark.title);
          await this.deleteBookmark(bookmarkId);
        }
        break;

      default:
        console.error('Unknown action:', action);
    }
  }

  async checkSingleBookmark(bookmark) {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'checkSingleBookmark',
        url: bookmark.url,
      });

      if (response.success) {
        this.results.set(bookmark.id, response.data);
        this.updateUI();
      }
    } catch (error) {
      console.error('Error checking bookmark:', error);
    }
  }

  async deleteBookmark(bookmarkId) {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'deleteBookmark',
        id: bookmarkId,
      });

      if (response.success) {
        this.bookmarks = this.bookmarks.filter((b) => b.id !== bookmarkId);
        this.results.delete(bookmarkId);
        this.updateUI();
      }
    } catch (error) {
      console.error('Error deleting bookmark:', error);
    }
  }

  updateScanButton() {
    const btn = document.getElementById('scanBtn');
    if (!btn) {
      return;
    }

    if (this.isChecking) {
      btn.innerHTML = '<span class="material-icons">pause</span>检测中...';
      btn.disabled = true;
    } else {
      btn.innerHTML = '<span class="material-icons">search</span>开始扫描';
      btn.disabled = false;
    }
  }

  showIncrementalInfo(checkedCount, totalCount) {
    // 创建增量检测提示
    const infoDiv = document.createElement('div');
    infoDiv.className = 'incremental-info';
    infoDiv.innerHTML = `
      <div class="info-content">
        <span class="material-icons">info</span>
        <span>增量检测: 检测 ${checkedCount} / ${totalCount} 个书签</span>
        <button class="close-info" onclick="this.parentElement.parentElement.remove()">
          <span class="material-icons">close</span>
        </button>
      </div>
    `;

    // 插入到统计信息下方
    const statsSection = document.querySelector('.stats');
    if (statsSection) {
      statsSection.insertAdjacentElement('afterend', infoDiv);

      // 3秒后自动移除
      setTimeout(() => {
        if (infoDiv.parentNode) {
          infoDiv.remove();
        }
      }, 3000);
    }
  }

  updateActionButtons() {
    const invalidCount = Array.from(this.results.values()).filter(
      (r) => r.status === 'invalid' || r.status === 'error'
    ).length;

    const deleteInvalidBtn = document.getElementById('deleteInvalidBtn');
    if (deleteInvalidBtn) {
      deleteInvalidBtn.disabled = invalidCount === 0;
      deleteInvalidBtn.textContent = `删除失效书签 (${invalidCount})`;
    }
  }

  async deleteInvalidBookmarks() {
    const invalidBookmarks = this.bookmarks.filter((bookmark) => {
      const result = this.results.get(bookmark.id);
      return (
        result && (result.status === 'invalid' || result.status === 'error')
      );
    });

    if (invalidBookmarks.length === 0) {
      return;
    }

    if (
      !confirm(
        `确定要删除 ${invalidBookmarks.length} 个失效书签吗？此操作不可撤销。`
      )
    ) {
      return;
    }

    for (const bookmark of invalidBookmarks) {
      await this.deleteBookmark(bookmark.id);
    }
  }

  exportReport() {
    const report = {
      timestamp: new Date().toISOString(),
      total: this.bookmarks.length,
      results: Array.from(this.results.entries()).map(([id, result]) => {
        const bookmark = this.bookmarks.find((b) => b.id === id);
        return {
          title: bookmark?.title,
          url: bookmark?.url,
          status: result.status,
          error: result.error,
        };
      }),
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], {
      type: 'application/json',
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bookmark-report-${
      new Date().toISOString().split('T')[0]
    }.json`;
    a.click();
    URL.revokeObjectURL(url);
  }

  showProgress() {
    const progressBar = this.createProgressBar();
    const header = document.querySelector('.header');
    if (!document.querySelector('.progress-bar')) {
      header.appendChild(progressBar);
    }
  }

  hideProgress() {
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
      progressBar.remove();
    }
  }

  updateProgress() {
    const progressBar = document.querySelector('.progress-bar');
    if (!progressBar) {
      return;
    }

    const total = this.bookmarks.length;
    const checked = this.results.size;
    const percentage = total > 0 ? (checked / total) * 100 : 0;

    const progressFill = progressBar.querySelector('.progress-fill');
    const progressText = progressBar.querySelector('.progress-text');

    if (progressFill) {
      progressFill.style.width = `${percentage}%`;
    }
    if (progressText) {
      progressText.textContent = `${checked}/${total} (${Math.round(
        percentage
      )}%)`;
    }
  }

  createProgressBar() {
    const progressBar = document.createElement('div');
    progressBar.className = 'progress-bar';
    progressBar.innerHTML = `
            <div class="progress-track">
                <div class="progress-fill"></div>
            </div>
            <div class="progress-text">0/0 (0%)</div>
        `;
    return progressBar;
  }

  // 显示成功消息
  showSuccess(message) {
    this.showMessage(message, 'success');
  }

  // 显示错误消息
  showError(message) {
    this.showMessage(message, 'error');
  }

  // 显示信息消息
  showInfo(message) {
    this.showMessage(message, 'info');
  }

  // 通用消息显示方法
  showMessage(message, type = 'info') {
    // 移除现有消息
    const existingMessage = document.querySelector('.message');
    if (existingMessage) {
      existingMessage.remove();
    }

    // 创建新消息
    const messageEl = document.createElement('div');
    messageEl.className = `message message-${type}`;
    messageEl.textContent = message;

    // 插入到页面顶部
    const container = document.querySelector('.container');
    container.insertBefore(messageEl, container.firstChild);

    // 3秒后自动移除
    setTimeout(() => {
      if (messageEl.parentNode) {
        messageEl.remove();
      }
    }, 3000);
  }

  toggleBatchMode() {
    this.batchMode = !this.batchMode;
    const batchActions = document.getElementById('batchActions');
    const toggleBtn = document.getElementById('toggleSelectBtn');

    if (this.batchMode) {
      if (batchActions) {
        batchActions.style.display = 'block';
      }
      if (toggleBtn) {
        toggleBtn.textContent = '退出批量选择';
        toggleBtn.classList.add('active');
      }
    } else {
      if (batchActions) {
        batchActions.style.display = 'none';
      }
      if (toggleBtn) {
        toggleBtn.textContent = '批量选择';
        toggleBtn.classList.remove('active');
      }
      this.selectedBookmarks.clear();
    }

    this.updateBookmarkList();
    this.updateSelectedCount();
  }

  selectAll() {
    const filteredBookmarks = this.getFilteredBookmarks();
    filteredBookmarks.forEach((bookmark) => {
      this.selectedBookmarks.add(bookmark.id);
    });
    this.updateBookmarkList();
    this.updateSelectedCount();
  }

  deselectAll() {
    this.selectedBookmarks.clear();
    this.updateBookmarkList();
    this.updateSelectedCount();
  }

  async deleteSelected() {
    if (this.selectedBookmarks.size === 0) {
      return;
    }

    if (
      !confirm(
        `确定要删除 ${this.selectedBookmarks.size} 个选中的书签吗？此操作不可撤销。`
      )
    ) {
      return;
    }

    for (const bookmarkId of this.selectedBookmarks) {
      await this.deleteBookmark(bookmarkId);
    }

    this.selectedBookmarks.clear();
    this.updateSelectedCount();
  }

  updateSelectedCount() {
    const selectedCount = document.getElementById('selectedCount');
    if (selectedCount) {
      selectedCount.textContent = this.selectedBookmarks.size;
    }
  }

  openSettings() {
    chrome.tabs.create({
      url: chrome.runtime.getURL('settings.html'),
    });
  }

  showReloadButton() {
    const container = document.getElementById('bookmarkList');
    if (!container) {
      return;
    }

    container.innerHTML = `
      <div class="error-state">
        <div class="icon">⚠️</div>
        <div class="message">扩展连接失败</div>
        <div class="description">后台脚本可能未正确加载</div>
        <button id="reloadExtensionBtn" class="btn btn-primary">
          <span class="icon">🔄</span>
          重新加载扩展
        </button>
        <button id="retryConnectionBtn" class="btn btn-secondary">
          <span class="icon">🔗</span>
          重试连接
        </button>
      </div>
    `;

    // 绑定重新加载按钮事件
    document
      .getElementById('reloadExtensionBtn')
      ?.addEventListener('click', () => {
        chrome.runtime.reload();
      });

    // 绑定重试连接按钮事件
    document
      .getElementById('retryConnectionBtn')
      ?.addEventListener('click', async () => {
        await this.loadBookmarks();
        await this.loadResults();
        this.updateUI();
      });
  }

  updateRedirectSuggestions() {
    const container = document.getElementById('redirectSuggestions');
    if (!container) {
      return;
    }

    if (this.redirectSuggestions.length === 0) {
      container.style.display = 'none';
      return;
    }

    container.style.display = 'block';
    container.innerHTML = `
      <div class="redirect-header">
        <h3>🔄 检测到重定向 (${this.redirectSuggestions.length})</h3>
        <p>以下书签已重定向到新地址，建议更新：</p>
      </div>
      <div class="redirect-list">
        ${this.redirectSuggestions.map((suggestion) => this.createRedirectSuggestionItem(suggestion)).join('')}
      </div>
      <div class="redirect-actions">
        <button id="updateAllRedirectsBtn" class="btn btn-primary">
          <span class="icon">🔄</span>
          更新所有重定向
        </button>
        <button id="dismissRedirectsBtn" class="btn btn-secondary">
          <span class="icon">❌</span>
          忽略建议
        </button>
      </div>
    `;

    // 绑定事件
    document
      .getElementById('updateAllRedirectsBtn')
      ?.addEventListener('click', () => {
        this.updateAllRedirects();
      });

    document
      .getElementById('dismissRedirectsBtn')
      ?.addEventListener('click', () => {
        this.dismissRedirectSuggestions();
      });
  }

  createRedirectSuggestionItem(suggestion) {
    const originalDomain = this.extractDomain(suggestion.originalUrl);
    const newDomain = suggestion.newDomain;

    return `
      <div class="redirect-item" data-bookmark-id="${suggestion.bookmarkId}">
        <div class="redirect-info">
          <div class="bookmark-title">${this.escapeHtml(suggestion.title)}</div>
          <div class="redirect-details">
            <div class="url-change">
              <span class="old-domain">${originalDomain}</span>
              <span class="arrow">→</span>
              <span class="new-domain">${newDomain}</span>
            </div>
            <div class="redirect-count">${suggestion.redirectCount} 次重定向</div>
          </div>
        </div>
        <div class="redirect-actions">
          <button class="btn btn-sm btn-primary update-single-btn"
                  data-bookmark-id="${suggestion.bookmarkId}"
                  data-new-url="${this.escapeHtml(suggestion.finalUrl)}">
            <span class="icon">✅</span>
            更新
          </button>
          <button class="btn btn-sm btn-secondary ignore-single-btn"
                  data-bookmark-id="${suggestion.bookmarkId}">
            <span class="icon">❌</span>
            忽略
          </button>
        </div>
      </div>
    `;
  }

  async updateAllRedirects() {
    if (this.redirectSuggestions.length === 0) {
      return;
    }

    const confirmMsg = `确定要更新 ${this.redirectSuggestions.length} 个重定向的书签吗？`;
    if (!confirm(confirmMsg)) {
      return;
    }

    for (const suggestion of this.redirectSuggestions) {
      await this.updateBookmarkUrl(suggestion.bookmarkId, suggestion.finalUrl);
    }

    this.redirectSuggestions = [];
    this.updateRedirectSuggestions();
    this.showSuccess('所有重定向书签已更新');
  }

  async updateBookmarkUrl(bookmarkId, newUrl) {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'updateBookmarkUrl',
        id: bookmarkId,
        newUrl: newUrl,
      });

      if (response.success) {
        // 更新本地数据
        const bookmark = this.bookmarks.find((b) => b.id === bookmarkId);
        if (bookmark) {
          bookmark.url = newUrl;
        }
        return true;
      }
    } catch (error) {
      console.error('Error updating bookmark URL:', error);
      return false;
    }
  }

  dismissRedirectSuggestions() {
    this.redirectSuggestions = [];
    this.updateRedirectSuggestions();
  }

  extractDomain(url) {
    try {
      return new URL(url).hostname;
    } catch {
      return url;
    }
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// 初始化面板
document.addEventListener('DOMContentLoaded', () => {
  new BookmarkPanel();
});
