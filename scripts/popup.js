// Popup脚本 - 处理用户界面交互

class BookmarkPanel {
  constructor() {
    this.bookmarks = [];
    this.results = new Map();
    this.currentFilter = 'all';
    this.searchTerm = '';
    this.isChecking = false;
    this.selectedBookmarks = new Set();
    this.batchMode = false;

    this.init();
  }

  async init() {
    this.bindEvents();
    await this.loadBookmarks();
    await this.loadResults();
    this.updateUI();

    // 监听来自background的消息
    chrome.runtime.onMessage.addListener((message) => {
      this.handleBackgroundMessage(message);
    });
  }

  bindEvents() {
    // 扫描按钮
    document.getElementById('scanBtn').addEventListener('click', () => {
      this.startScanning();
    });

    // 设置按钮
    document.getElementById('settingsBtn').addEventListener('click', () => {
      this.openSettings();
    });

    // 过滤按钮
    document.querySelectorAll('.filter-btn').forEach((btn) => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const filter = e.target.dataset.filter;
        console.log('Filter button clicked:', filter);
        this.setFilter(filter);
      });
    });

    // 搜索框
    document.getElementById('searchInput').addEventListener('input', (e) => {
      this.searchTerm = e.target.value.toLowerCase();
      this.updateBookmarkList();
    });

    // 删除失效书签按钮
    document
      .getElementById('deleteInvalidBtn')
      .addEventListener('click', () => {
        this.deleteInvalidBookmarks();
      });

    // 导出报告按钮
    document.getElementById('exportBtn').addEventListener('click', () => {
      this.exportReport();
    });

    // 批量选择相关按钮
    document.getElementById('toggleSelectBtn').addEventListener('click', () => {
      this.toggleBatchMode();
    });

    document.getElementById('selectAllBtn').addEventListener('click', () => {
      this.selectAll();
    });

    document.getElementById('deselectAllBtn').addEventListener('click', () => {
      this.deselectAll();
    });

    document
      .getElementById('deleteSelectedBtn')
      .addEventListener('click', () => {
        this.deleteSelected();
      });
  }

  async loadBookmarks() {
    try {
      // 检查扩展是否正常运行
      if (!chrome.runtime || !chrome.runtime.sendMessage) {
        throw new Error('Chrome runtime not available');
      }

      console.log('Attempting to load bookmarks...');
      const response = await chrome.runtime.sendMessage({
        action: 'getBookmarks',
      });

      if (response && response.success) {
        this.bookmarks = response.data;
        console.log(`Loaded ${this.bookmarks.length} bookmarks`);
      } else {
        const errorMsg = response
          ? response.error
          : 'No response from background script';
        console.error('Failed to load bookmarks:', errorMsg);
        this.showError('加载书签失败: ' + errorMsg);
      }
    } catch (error) {
      console.error('Error loading bookmarks:', error);

      // 提供更详细的错误信息
      let errorMessage = '加载书签时发生错误';
      if (error.message.includes('Could not establish connection')) {
        errorMessage = '无法连接到后台脚本，请尝试重新加载扩展';
      } else if (error.message.includes('Extension context invalidated')) {
        errorMessage = '扩展上下文已失效，请重新加载扩展';
      }

      this.showError(errorMessage);

      // 显示重新加载按钮
      this.showReloadButton();
    }
  }

  async loadResults() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'getResults',
      });

      if (response.success) {
        this.results = new Map(response.data);
        console.log(`Loaded ${this.results.size} results`);
      }
    } catch (error) {
      console.error('Error loading results:', error);
    }
  }

  handleBackgroundMessage(message) {
    switch (message.type) {
      case 'bookmarkChecked':
        this.results.set(message.data.id, message.data.result);
        this.updateUI();
        this.updateProgress();
        break;

      case 'checkingComplete':
        this.isChecking = false;
        this.updateScanButton();
        this.updateStats();
        this.hideProgress();
        break;

      case 'checkingStarted':
        this.isChecking = true;
        this.updateScanButton();
        this.showProgress();
        break;
    }
  }

  async startScanning() {
    if (this.isChecking) {
      return;
    }

    this.isChecking = true;
    this.updateScanButton();

    // 清除旧结果
    this.results.clear();

    try {
      await chrome.runtime.sendMessage({
        action: 'checkBookmarks',
        bookmarks: this.bookmarks,
      });
    } catch (error) {
      console.error('Error starting scan:', error);
      this.isChecking = false;
      this.updateScanButton();
    }
  }

  setFilter(filter) {
    console.log('Setting filter to:', filter);
    this.currentFilter = filter;

    // 更新按钮状态
    document.querySelectorAll('.filter-btn').forEach((btn) => {
      btn.classList.toggle('active', btn.dataset.filter === filter);
    });

    this.updateBookmarkList();
  }

  updateUI() {
    this.updateStats();
    this.updateBookmarkList();
    this.updateActionButtons();
  }

  updateStats() {
    const total = this.bookmarks.length;
    let valid = 0,
      invalid = 0,
      checking = 0;

    for (const bookmark of this.bookmarks) {
      const result = this.results.get(bookmark.id);
      if (!result) {
        continue;
      }

      switch (result.status) {
        case 'valid':
          valid++;
          break;
        case 'invalid':
        case 'error':
          invalid++;
          break;
        case 'warning':
          // 警告状态算作有效但需要注意
          valid++;
          break;
        case 'checking':
          checking++;
          break;
      }
    }

    // 安全更新统计数据
    const totalCountEl = document.getElementById('totalCount');
    const validCountEl = document.getElementById('validCount');
    const invalidCountEl = document.getElementById('invalidCount');
    const checkingCountEl = document.getElementById('checkingCount');

    if (totalCountEl) {
      totalCountEl.textContent = total;
    }
    if (validCountEl) {
      validCountEl.textContent = valid;
    }
    if (invalidCountEl) {
      invalidCountEl.textContent = invalid;
    }
    if (checkingCountEl) {
      checkingCountEl.textContent = checking;
    }
  }

  updateBookmarkList() {
    const container = document.getElementById('bookmarkList');
    const loading = document.getElementById('loadingIndicator');

    if (this.bookmarks.length === 0) {
      if (loading) {
        loading.style.display = 'flex';
      }
      return;
    }

    if (loading) {
      loading.style.display = 'none';
    }

    const filteredBookmarks = this.getFilteredBookmarks();

    if (filteredBookmarks.length === 0) {
      container.innerHTML = `
                <div class="empty-state">
                    <div class="icon">📭</div>
                    <div>没有找到匹配的书签</div>
                </div>
            `;
      return;
    }

    container.innerHTML = filteredBookmarks
      .map((bookmark) => this.createBookmarkItem(bookmark))
      .join('');

    // 绑定书签项目事件
    this.bindBookmarkEvents();
  }

  getFilteredBookmarks() {
    return this.bookmarks.filter((bookmark) => {
      // 搜索过滤
      if (this.searchTerm) {
        const searchMatch =
          bookmark.title.toLowerCase().includes(this.searchTerm) ||
          bookmark.url.toLowerCase().includes(this.searchTerm);
        if (!searchMatch) {
          return false;
        }
      }

      // 状态过滤
      if (this.currentFilter === 'all') {
        return true;
      }

      const result = this.results.get(bookmark.id);

      switch (this.currentFilter) {
        case 'valid':
          return (
            result && (result.status === 'valid' || result.status === 'warning')
          );
        case 'invalid':
          return (
            result && (result.status === 'invalid' || result.status === 'error')
          );
        case 'unchecked':
          return !result;
        default:
          return true;
      }
    });
  }

  createBookmarkItem(bookmark) {
    const result = this.results.get(bookmark.id);
    const status = result ? result.status : 'unchecked';
    const error = result && result.error ? result.error : '';
    const responseTime =
      result && result.responseTime ? result.responseTime : null;
    const statusCode = result && result.statusCode ? result.statusCode : null;

    // 转义HTML特殊字符
    const escapeHtml = (text) => {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    };

    const safeTitle = escapeHtml(bookmark.title || 'Untitled');
    const safeUrl = escapeHtml(bookmark.url);
    const safeError = error ? escapeHtml(error) : '';

    let statusInfo = '';
    let methodInfo = '';
    if (result) {
      if (statusCode) {
        statusInfo += ` (${statusCode})`;
      }
      if (responseTime) {
        statusInfo += ` - ${responseTime}ms`;
      }
      if (result.method) {
        methodInfo = ` [${result.method}]`;
      }
      if (result.note) {
        statusInfo += ` - ${result.note}`;
      }
    }

    const isSelected = this.selectedBookmarks.has(bookmark.id);
    const checkboxHtml = this.batchMode
      ? `<input type="checkbox" class="bookmark-checkbox" ${
        isSelected ? 'checked' : ''
      } data-bookmark-id="${bookmark.id}">`
      : '';

    return `
            <div class="bookmark-item ${
  isSelected ? 'selected' : ''
}" data-id="${bookmark.id}">
                <div class="bookmark-header">
                    ${checkboxHtml}
                    <div class="bookmark-status ${status}" title="${status}${statusInfo}${methodInfo}"></div>
                    <div class="bookmark-title" title="${safeTitle}">${safeTitle}</div>
                    <div class="bookmark-actions">
                        <button class="action-btn" data-action="open" title="打开链接">🔗</button>
                        <button class="action-btn" data-action="check" title="重新检测">🔄</button>
                        <button class="action-btn" data-action="delete" title="删除书签">🗑️</button>
                    </div>
                </div>
                <div class="bookmark-url" title="${safeUrl}">${safeUrl}</div>
                ${
  error
    ? `<div class="bookmark-error" title="${safeError}">${safeError}</div>`
    : ''
}
            </div>
        `;
  }

  bindBookmarkEvents() {
    document.querySelectorAll('.action-btn').forEach((btn) => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const action = e.target.dataset.action;
        const bookmarkId = e.target.closest('.bookmark-item').dataset.id;
        this.handleBookmarkAction(action, bookmarkId);
      });
    });

    // 绑定复选框事件
    document.querySelectorAll('.bookmark-checkbox').forEach((checkbox) => {
      checkbox.addEventListener('change', (e) => {
        const bookmarkId = e.target.dataset.bookmarkId;
        if (e.target.checked) {
          this.selectedBookmarks.add(bookmarkId);
        } else {
          this.selectedBookmarks.delete(bookmarkId);
        }
        this.updateSelectedCount();

        // 更新书签项的选中状态
        const bookmarkItem = e.target.closest('.bookmark-item');
        bookmarkItem.classList.toggle('selected', e.target.checked);
      });
    });
  }

  async handleBookmarkAction(action, bookmarkId) {
    const bookmark = this.bookmarks.find((b) => b.id === bookmarkId);
    if (!bookmark) {
      return;
    }

    switch (action) {
      case 'open':
        chrome.tabs.create({ url: bookmark.url });
        break;

      case 'check':
        await this.checkSingleBookmark(bookmark);
        break;

      case 'delete':
        if (confirm(`确定要删除书签 "${bookmark.title}" 吗？`)) {
          await this.deleteBookmark(bookmarkId);
        }
        break;
    }
  }

  async checkSingleBookmark(bookmark) {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'checkSingleBookmark',
        url: bookmark.url,
      });

      if (response.success) {
        this.results.set(bookmark.id, response.data);
        this.updateUI();
      }
    } catch (error) {
      console.error('Error checking bookmark:', error);
    }
  }

  async deleteBookmark(bookmarkId) {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'deleteBookmark',
        id: bookmarkId,
      });

      if (response.success) {
        this.bookmarks = this.bookmarks.filter((b) => b.id !== bookmarkId);
        this.results.delete(bookmarkId);
        this.updateUI();
      }
    } catch (error) {
      console.error('Error deleting bookmark:', error);
    }
  }

  updateScanButton() {
    const btn = document.getElementById('scanBtn');
    if (!btn) {
      return;
    }

    if (this.isChecking) {
      btn.innerHTML = '<span class="icon">⏸️</span>检测中...';
      btn.disabled = true;
    } else {
      btn.innerHTML = '<span class="icon">🔍</span>开始扫描';
      btn.disabled = false;
    }
  }

  updateActionButtons() {
    const invalidCount = Array.from(this.results.values()).filter(
      (r) => r.status === 'invalid' || r.status === 'error',
    ).length;

    const deleteInvalidBtn = document.getElementById('deleteInvalidBtn');
    if (deleteInvalidBtn) {
      deleteInvalidBtn.disabled = invalidCount === 0;
      deleteInvalidBtn.textContent = `删除失效书签 (${invalidCount})`;
    }
  }

  async deleteInvalidBookmarks() {
    const invalidBookmarks = this.bookmarks.filter((bookmark) => {
      const result = this.results.get(bookmark.id);
      return (
        result && (result.status === 'invalid' || result.status === 'error')
      );
    });

    if (invalidBookmarks.length === 0) {
      return;
    }

    if (
      !confirm(
        `确定要删除 ${invalidBookmarks.length} 个失效书签吗？此操作不可撤销。`,
      )
    ) {
      return;
    }

    for (const bookmark of invalidBookmarks) {
      await this.deleteBookmark(bookmark.id);
    }
  }

  exportReport() {
    const report = {
      timestamp: new Date().toISOString(),
      total: this.bookmarks.length,
      results: Array.from(this.results.entries()).map(([id, result]) => {
        const bookmark = this.bookmarks.find((b) => b.id === id);
        return {
          title: bookmark?.title,
          url: bookmark?.url,
          status: result.status,
          error: result.error,
        };
      }),
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], {
      type: 'application/json',
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bookmark-report-${
      new Date().toISOString().split('T')[0]
    }.json`;
    a.click();
    URL.revokeObjectURL(url);
  }

  showProgress() {
    const progressBar = this.createProgressBar();
    const header = document.querySelector('.header');
    if (!document.querySelector('.progress-bar')) {
      header.appendChild(progressBar);
    }
  }

  hideProgress() {
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
      progressBar.remove();
    }
  }

  updateProgress() {
    const progressBar = document.querySelector('.progress-bar');
    if (!progressBar) {
      return;
    }

    const total = this.bookmarks.length;
    const checked = this.results.size;
    const percentage = total > 0 ? (checked / total) * 100 : 0;

    const progressFill = progressBar.querySelector('.progress-fill');
    const progressText = progressBar.querySelector('.progress-text');

    if (progressFill) {
      progressFill.style.width = `${percentage}%`;
    }
    if (progressText) {
      progressText.textContent = `${checked}/${total} (${Math.round(
        percentage,
      )}%)`;
    }
  }

  createProgressBar() {
    const progressBar = document.createElement('div');
    progressBar.className = 'progress-bar';
    progressBar.innerHTML = `
            <div class="progress-track">
                <div class="progress-fill"></div>
            </div>
            <div class="progress-text">0/0 (0%)</div>
        `;
    return progressBar;
  }

  toggleBatchMode() {
    this.batchMode = !this.batchMode;
    const batchActions = document.getElementById('batchActions');
    const toggleBtn = document.getElementById('toggleSelectBtn');

    if (this.batchMode) {
      if (batchActions) {
        batchActions.style.display = 'block';
      }
      if (toggleBtn) {
        toggleBtn.textContent = '退出批量选择';
        toggleBtn.classList.add('active');
      }
    } else {
      if (batchActions) {
        batchActions.style.display = 'none';
      }
      if (toggleBtn) {
        toggleBtn.textContent = '批量选择';
        toggleBtn.classList.remove('active');
      }
      this.selectedBookmarks.clear();
    }

    this.updateBookmarkList();
    this.updateSelectedCount();
  }

  selectAll() {
    const filteredBookmarks = this.getFilteredBookmarks();
    filteredBookmarks.forEach((bookmark) => {
      this.selectedBookmarks.add(bookmark.id);
    });
    this.updateBookmarkList();
    this.updateSelectedCount();
  }

  deselectAll() {
    this.selectedBookmarks.clear();
    this.updateBookmarkList();
    this.updateSelectedCount();
  }

  async deleteSelected() {
    if (this.selectedBookmarks.size === 0) {
      return;
    }

    if (
      !confirm(
        `确定要删除 ${this.selectedBookmarks.size} 个选中的书签吗？此操作不可撤销。`,
      )
    ) {
      return;
    }

    for (const bookmarkId of this.selectedBookmarks) {
      await this.deleteBookmark(bookmarkId);
    }

    this.selectedBookmarks.clear();
    this.updateSelectedCount();
  }

  updateSelectedCount() {
    const selectedCount = document.getElementById('selectedCount');
    if (selectedCount) {
      selectedCount.textContent = this.selectedBookmarks.size;
    }
  }

  showError(message) {
    this.showMessage(message, 'error');
  }

  showSuccess(message) {
    this.showMessage(message, 'success');
  }

  showMessage(text, type = 'info') {
    // 移除现有消息
    const existingMessage = document.querySelector('.message');
    if (existingMessage) {
      existingMessage.remove();
    }

    // 创建新消息
    const message = document.createElement('div');
    message.className = `message message-${type}`;
    message.textContent = text;

    // 插入到容器顶部
    const container = document.querySelector('.container');
    container.insertBefore(message, container.firstChild);

    // 3秒后自动移除
    setTimeout(() => {
      if (message.parentNode) {
        message.remove();
      }
    }, 3000);
  }

  openSettings() {
    chrome.tabs.create({
      url: chrome.runtime.getURL('settings.html'),
    });
  }

  showReloadButton() {
    const container = document.getElementById('bookmarkList');
    if (!container) {
      return;
    }

    container.innerHTML = `
      <div class="error-state">
        <div class="icon">⚠️</div>
        <div class="message">扩展连接失败</div>
        <div class="description">后台脚本可能未正确加载</div>
        <button id="reloadExtensionBtn" class="btn btn-primary">
          <span class="icon">🔄</span>
          重新加载扩展
        </button>
        <button id="retryConnectionBtn" class="btn btn-secondary">
          <span class="icon">🔗</span>
          重试连接
        </button>
      </div>
    `;

    // 绑定重新加载按钮事件
    document
      .getElementById('reloadExtensionBtn')
      ?.addEventListener('click', () => {
        chrome.runtime.reload();
      });

    // 绑定重试连接按钮事件
    document
      .getElementById('retryConnectionBtn')
      ?.addEventListener('click', async() => {
        await this.loadBookmarks();
        await this.loadResults();
        this.updateUI();
      });
  }
}

// 初始化面板
document.addEventListener('DOMContentLoaded', () => {
  new BookmarkPanel();
});
