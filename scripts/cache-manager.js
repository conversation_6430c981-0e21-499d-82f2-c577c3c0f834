/**
 * 智能缓存管理器
 * 根据检测结果状态设置不同的缓存时间，优化检测性能
 */
class CacheManager {
  constructor() {
    this.cacheDuration = {
      valid: 7 * 24 * 60 * 60 * 1000, // 有效书签缓存7天
      invalid: 1 * 24 * 60 * 60 * 1000, // 失效书签缓存1天
      error: 1 * 60 * 60 * 1000, // 错误书签缓存1小时
      warning: 3 * 24 * 60 * 60 * 1000, // 警告书签缓存3天
      checking: 0, // 检测中状态不缓存
    };

    this.maxCacheSize = 10000; // 最大缓存条目数
    this.cleanupInterval = 60 * 60 * 1000; // 1小时清理一次过期缓存

    this.init();
  }

  /**
   * 初始化缓存管理器
   */
  async init() {
    // 启动定期清理
    this.startPeriodicCleanup();

    // 加载缓存设置
    await this.loadCacheSettings();
  }

  /**
   * 检查缓存是否有效
   */
  async isCacheValid(bookmarkId) {
    try {
      const results = await self.storageManager.getBookmarkResults();
      const result = results[bookmarkId];

      if (!result || !result.cachedAt) {
        return false;
      }

      const now = Date.now();
      const cacheAge = now - result.cachedAt;
      const maxAge = this.getCacheDuration(result.status);

      return cacheAge < maxAge;
    } catch (error) {
      console.error('检查缓存有效性失败:', error);
      return false;
    }
  }

  /**
   * 获取缓存的检测结果
   */
  async getCachedResult(bookmarkId) {
    try {
      const isValid = await this.isCacheValid(bookmarkId);
      if (!isValid) {
        return null;
      }

      const results = await self.storageManager.getBookmarkResults();
      const result = results[bookmarkId];

      if (result) {
        // 更新访问时间
        result.lastAccessed = Date.now();
        await this.updateCacheEntry(bookmarkId, result);

        console.log(`缓存命中: ${bookmarkId} (${result.status})`);
        return result;
      }

      return null;
    } catch (error) {
      console.error('获取缓存结果失败:', error);
      return null;
    }
  }

  /**
   * 缓存检测结果
   */
  async setCachedResult(bookmarkId, result) {
    try {
      const now = Date.now();
      const cachedResult = {
        ...result,
        cachedAt: now,
        lastAccessed: now,
        cacheVersion: 1,
      };

      await this.updateCacheEntry(bookmarkId, cachedResult);

      console.log(`缓存保存: ${bookmarkId} (${result.status})`);

      // 检查缓存大小，必要时清理
      await this.checkCacheSize();
    } catch (error) {
      console.error('缓存检测结果失败:', error);
    }
  }

  /**
   * 更新单个缓存条目
   */
  async updateCacheEntry(bookmarkId, result) {
    try {
      const results = await self.storageManager.getBookmarkResults();
      results[bookmarkId] = result;
      await self.storageManager.setBookmarkResults(results);
    } catch (error) {
      console.error('更新缓存条目失败:', error);
    }
  }

  /**
   * 根据状态获取缓存持续时间
   */
  getCacheDuration(status) {
    return this.cacheDuration[status] || this.cacheDuration.error;
  }

  /**
   * 批量检查哪些书签需要重新检测
   */
  async getBookmarksNeedingCheck(bookmarks) {
    const needsCheck = [];
    const cached = [];

    for (const bookmark of bookmarks) {
      const cachedResult = await this.getCachedResult(bookmark.id);

      if (cachedResult) {
        cached.push({
          bookmark,
          result: cachedResult,
        });
      } else {
        needsCheck.push(bookmark);
      }
    }

    console.log(
      `缓存统计: ${cached.length} 个缓存命中, ${needsCheck.length} 个需要检测`,
    );

    return {
      needsCheck,
      cached,
    };
  }

  /**
   * 清理过期的缓存
   */
  async cleanupExpiredCache() {
    try {
      const results = await self.storageManager.getBookmarkResults();

      const now = Date.now();
      let cleanedCount = 0;

      for (const [bookmarkId, result] of Object.entries(results)) {
        if (!result.cachedAt) {
          continue;
        }

        const cacheAge = now - result.cachedAt;
        const maxAge = this.getCacheDuration(result.status);

        if (cacheAge > maxAge) {
          delete results[bookmarkId];
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        await self.storageManager.setBookmarkResults(results);
        console.log(`清理了 ${cleanedCount} 个过期缓存条目`);
      }

      return cleanedCount;
    } catch (error) {
      console.error('清理过期缓存失败:', error);
      return 0;
    }
  }

  /**
   * 检查缓存大小并清理最旧的条目
   */
  async checkCacheSize() {
    try {
      const results = await self.storageManager.getBookmarkResults();

      const entries = Object.entries(results);

      if (entries.length <= this.maxCacheSize) {
        return;
      }

      // 按最后访问时间排序，删除最旧的条目
      entries.sort((a, b) => {
        const aTime = a[1].lastAccessed || a[1].cachedAt || 0;
        const bTime = b[1].lastAccessed || b[1].cachedAt || 0;
        return aTime - bTime;
      });

      const toDelete = entries.length - this.maxCacheSize;
      const newResults = {};

      // 保留最新的条目
      for (let i = toDelete; i < entries.length; i++) {
        const [bookmarkId, result] = entries[i];
        newResults[bookmarkId] = result;
      }

      await self.storageManager.setBookmarkResults(newResults);
      console.log(`缓存大小限制: 删除了 ${toDelete} 个最旧的缓存条目`);
    } catch (error) {
      console.error('检查缓存大小失败:', error);
    }
  }

  /**
   * 启动定期清理
   */
  startPeriodicCleanup() {
    setInterval(async() => {
      await this.cleanupExpiredCache();
    }, this.cleanupInterval);

    console.log('缓存定期清理已启动');
  }

  /**
   * 加载缓存设置
   */
  async loadCacheSettings() {
    try {
      const stored = await chrome.storage.local.get(['cacheSettings']);
      if (stored.cacheSettings) {
        this.cacheDuration = {
          ...this.cacheDuration,
          ...stored.cacheSettings.duration,
        };
        this.maxCacheSize = stored.cacheSettings.maxSize || this.maxCacheSize;
      }
    } catch (error) {
      console.error('加载缓存设置失败:', error);
    }
  }

  /**
   * 保存缓存设置
   */
  async saveCacheSettings(settings) {
    try {
      const cacheSettings = {
        duration: settings.duration || this.cacheDuration,
        maxSize: settings.maxSize || this.maxCacheSize,
      };

      await chrome.storage.local.set({ cacheSettings });

      // 更新当前设置
      if (settings.duration) {
        this.cacheDuration = { ...this.cacheDuration, ...settings.duration };
      }
      if (settings.maxSize) {
        this.maxCacheSize = settings.maxSize;
      }

      console.log('缓存设置已保存');
    } catch (error) {
      console.error('保存缓存设置失败:', error);
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats() {
    try {
      const results = await self.storageManager.getBookmarkResults();

      const stats = {
        total: 0,
        valid: 0,
        invalid: 0,
        error: 0,
        warning: 0,
        expired: 0,
        totalSize: 0,
      };

      const now = Date.now();

      for (const [, result] of Object.entries(results)) {
        stats.total++;

        if (result.status) {
          stats[result.status] = (stats[result.status] || 0) + 1;
        }

        // 检查是否过期
        if (result.cachedAt) {
          const cacheAge = now - result.cachedAt;
          const maxAge = this.getCacheDuration(result.status);

          if (cacheAge > maxAge) {
            stats.expired++;
          }
        }

        // 估算大小（粗略）
        stats.totalSize += JSON.stringify(result).length;
      }

      return stats;
    } catch (error) {
      console.error('获取缓存统计失败:', error);
      return null;
    }
  }

  /**
   * 清空所有缓存
   */
  async clearAllCache() {
    try {
      await self.storageManager.setBookmarkResults({});
      console.log('所有缓存已清空');
      return true;
    } catch (error) {
      console.error('清空缓存失败:', error);
      return false;
    }
  }

  /**
   * 预热缓存 - 为常用书签预加载结果
   */
  async warmupCache(bookmarks) {
    console.log(`开始预热缓存，共 ${bookmarks.length} 个书签`);

    // 这里可以实现预热逻辑，比如优先检测最近访问的书签
    // 暂时只是记录日志
  }
}

// 全局实例
self.cacheManager = new CacheManager();
