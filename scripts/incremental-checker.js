/**
 * 增量检测管理器
 * 负责管理书签的增量检测功能，只检测变化的书签
 */
class IncrementalChecker {
  constructor() {
    this.lastCheckTime = null;
    this.bookmarkHashes = new Map(); // 存储书签的哈希值
    this.changeThreshold = 5 * 60 * 1000; // 5分钟内的变化才算新变化
  }

  /**
   * 初始化增量检测器
   */
  async init() {
    await this.loadLastCheckTime();
    await this.loadBookmarkHashes();
  }

  /**
   * 获取需要检测的书签（增量）
   */
  async getBookmarksToCheck(allBookmarks) {
    const now = Date.now();
    const changedBookmarks = [];

    // 如果是第一次检测，检测所有书签
    if (!this.lastCheckTime) {
      console.log('首次检测，将检测所有书签');
      await this.updateLastCheckTime(now);
      return allBookmarks;
    }

    // 检测新增、修改、删除的书签
    for (const bookmark of allBookmarks) {
      if (this.isBookmarkChanged(bookmark)) {
        changedBookmarks.push(bookmark);
      }
    }

    // 更新书签哈希值
    await this.updateBookmarkHashes(allBookmarks);
    await this.updateLastCheckTime(now);

    console.log(`增量检测: 发现 ${changedBookmarks.length} 个变化的书签`);
    return changedBookmarks;
  }

  /**
   * 检查书签是否发生变化
   */
  isBookmarkChanged(bookmark) {
    const currentHash = this.generateBookmarkHash(bookmark);
    const storedHash = this.bookmarkHashes.get(bookmark.id);

    // 新书签
    if (!storedHash) {
      return true;
    }

    // 书签内容发生变化
    if (currentHash !== storedHash) {
      return true;
    }

    // 书签最近被修改
    if (bookmark.dateAdded && bookmark.dateAdded > this.lastCheckTime) {
      return true;
    }

    return false;
  }

  /**
   * 生成书签的哈希值
   */
  generateBookmarkHash(bookmark) {
    const content = `${bookmark.title}|${bookmark.url}|${bookmark.parentId}`;
    return this.simpleHash(content);
  }

  /**
   * 简单哈希函数
   */
  simpleHash(str) {
    let hash = 0;
    if (str.length === 0) {return hash;}

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }

    return hash.toString();
  }

  /**
   * 获取基于时间的变化书签
   */
  async getRecentlyChangedBookmarks(allBookmarks, timeWindow = 24 * 60 * 60 * 1000) {
    const cutoffTime = Date.now() - timeWindow;

    return allBookmarks.filter(bookmark => {
      // 检查添加时间
      if (bookmark.dateAdded && bookmark.dateAdded > cutoffTime) {
        return true;
      }

      // 检查修改时间（如果有的话）
      if (bookmark.dateModified && bookmark.dateModified > cutoffTime) {
        return true;
      }

      return false;
    });
  }

  /**
   * 获取失效书签的重新检测列表
   */
  async getInvalidBookmarksForRecheck(allBookmarks) {
    try {
      const stored = await chrome.storage.local.get(['bookmarkResults']);
      const results = stored.bookmarkResults || {};

      const invalidBookmarks = [];
      const recheckInterval = 24 * 60 * 60 * 1000; // 24小时重新检测失效书签
      const now = Date.now();

      for (const bookmark of allBookmarks) {
        const result = results[bookmark.id];

        if (result &&
            (result.status === 'invalid' || result.status === 'error') &&
            (!result.lastChecked || (now - result.lastChecked) > recheckInterval)) {
          invalidBookmarks.push(bookmark);
        }
      }

      console.log(`找到 ${invalidBookmarks.length} 个需要重新检测的失效书签`);
      return invalidBookmarks;
    } catch (error) {
      console.error('获取失效书签列表失败:', error);
      return [];
    }
  }

  /**
   * 智能选择检测策略
   */
  async getSmartCheckList(allBookmarks, options = {}) {
    const {
      forceAll = false,
      includeInvalid = true,
      maxBookmarks = 100,
      prioritizeRecent = true,
    } = options;

    if (forceAll) {
      return allBookmarks;
    }

    let bookmarksToCheck = [];

    // 1. 获取增量变化的书签
    const changedBookmarks = await this.getBookmarksToCheck(allBookmarks);
    bookmarksToCheck = bookmarksToCheck.concat(changedBookmarks);

    // 2. 如果启用，添加失效书签的重新检测
    if (includeInvalid) {
      const invalidBookmarks = await this.getInvalidBookmarksForRecheck(allBookmarks);
      bookmarksToCheck = bookmarksToCheck.concat(invalidBookmarks);
    }

    // 3. 如果优先检测最近的书签
    if (prioritizeRecent && bookmarksToCheck.length > maxBookmarks) {
      bookmarksToCheck.sort((a, b) => {
        const aTime = Math.max(a.dateAdded || 0, a.dateModified || 0);
        const bTime = Math.max(b.dateAdded || 0, b.dateModified || 0);
        return bTime - aTime; // 降序排列
      });
      bookmarksToCheck = bookmarksToCheck.slice(0, maxBookmarks);
    }

    // 4. 去重
    const uniqueBookmarks = this.removeDuplicateBookmarks(bookmarksToCheck);

    console.log(`智能检测策略: 选择了 ${uniqueBookmarks.length} 个书签进行检测`);
    return uniqueBookmarks;
  }

  /**
   * 去除重复的书签
   */
  removeDuplicateBookmarks(bookmarks) {
    const seen = new Set();
    return bookmarks.filter(bookmark => {
      if (seen.has(bookmark.id)) {
        return false;
      }
      seen.add(bookmark.id);
      return true;
    });
  }

  /**
   * 加载上次检测时间
   */
  async loadLastCheckTime() {
    try {
      const stored = await chrome.storage.local.get(['lastIncrementalCheck']);
      this.lastCheckTime = stored.lastIncrementalCheck || null;
    } catch (error) {
      console.error('加载上次检测时间失败:', error);
    }
  }

  /**
   * 更新上次检测时间
   */
  async updateLastCheckTime(time) {
    try {
      this.lastCheckTime = time;
      await chrome.storage.local.set({ lastIncrementalCheck: time });
    } catch (error) {
      console.error('更新检测时间失败:', error);
    }
  }

  /**
   * 加载书签哈希值
   */
  async loadBookmarkHashes() {
    try {
      const stored = await chrome.storage.local.get(['bookmarkHashes']);
      if (stored.bookmarkHashes) {
        this.bookmarkHashes = new Map(Object.entries(stored.bookmarkHashes));
      }
    } catch (error) {
      console.error('加载书签哈希值失败:', error);
    }
  }

  /**
   * 更新书签哈希值
   */
  async updateBookmarkHashes(bookmarks) {
    try {
      // 清除旧的哈希值
      this.bookmarkHashes.clear();

      // 生成新的哈希值
      for (const bookmark of bookmarks) {
        const hash = this.generateBookmarkHash(bookmark);
        this.bookmarkHashes.set(bookmark.id, hash);
      }

      // 保存到存储
      const hashObject = Object.fromEntries(this.bookmarkHashes);
      await chrome.storage.local.set({ bookmarkHashes: hashObject });
    } catch (error) {
      console.error('更新书签哈希值失败:', error);
    }
  }

  /**
   * 获取检测统计信息
   */
  getStats() {
    return {
      lastCheckTime: this.lastCheckTime,
      trackedBookmarks: this.bookmarkHashes.size,
      nextIncrementalCheck: this.lastCheckTime ?
        new Date(this.lastCheckTime + this.changeThreshold).toLocaleString() :
        '未设置',
    };
  }

  /**
   * 重置增量检测状态
   */
  async reset() {
    this.lastCheckTime = null;
    this.bookmarkHashes.clear();

    try {
      await chrome.storage.local.remove(['lastIncrementalCheck', 'bookmarkHashes']);
      console.log('增量检测状态已重置');
    } catch (error) {
      console.error('重置增量检测状态失败:', error);
    }
  }
}

// 全局实例
window.incrementalChecker = new IncrementalChecker();
