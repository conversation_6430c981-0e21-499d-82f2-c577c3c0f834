/**
 * 存储管理器
 * 使用IndexedDB替代localStorage，支持更大数据量和更好的性能
 */
class StorageManager {
  constructor() {
    this.dbName = 'BookmarkCheckerDB';
    this.dbVersion = 1;
    this.db = null;

    this.stores = {
      bookmarkResults: 'bookmarkResults',
      settings: 'settings',
      cache: 'cache',
      scheduledCheck: 'scheduledCheck',
      incrementalCheck: 'incrementalCheck',
    };
  }

  /**
   * 初始化数据库
   */
  async init() {
    try {
      this.db = await this.openDatabase();
      console.log('IndexedDB 初始化成功');

      // 迁移现有的localStorage数据
      await this.migrateFromLocalStorage();

      return true;
    } catch (error) {
      console.error('IndexedDB 初始化失败，回退到localStorage:', error);
      return false;
    }
  }

  /**
   * 打开数据库
   */
  openDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        reject(new Error('无法打开IndexedDB'));
      };

      request.onsuccess = (event) => {
        resolve(event.target.result);
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;

        // 创建对象存储
        Object.values(this.stores).forEach((storeName) => {
          if (!db.objectStoreNames.contains(storeName)) {
            const store = db.createObjectStore(storeName, { keyPath: 'id' });

            // 为某些存储添加索引
            if (storeName === this.stores.bookmarkResults) {
              store.createIndex('status', 'status', { unique: false });
              store.createIndex('lastChecked', 'lastChecked', {
                unique: false,
              });
            }
          }
        });
      };
    });
  }

  /**
   * 从localStorage迁移数据
   */
  async migrateFromLocalStorage() {
    try {
      // 迁移书签检测结果
      const bookmarkResults = localStorage.getItem('bookmarkResults');
      if (bookmarkResults) {
        const results = JSON.parse(bookmarkResults);
        await this.setBookmarkResults(results);
        localStorage.removeItem('bookmarkResults');
        console.log('已迁移书签检测结果到IndexedDB');
      }

      // 迁移设置
      const settings = localStorage.getItem('settings');
      if (settings) {
        await this.setSettings(JSON.parse(settings));
        localStorage.removeItem('settings');
        console.log('已迁移设置到IndexedDB');
      }

      // 迁移其他数据
      const otherKeys = [
        'scheduledCheck',
        'lastIncrementalCheck',
        'bookmarkHashes',
        'cacheSettings',
      ];
      for (const key of otherKeys) {
        const data = localStorage.getItem(key);
        if (data) {
          await this.set(key, JSON.parse(data));
          localStorage.removeItem(key);
        }
      }
    } catch (error) {
      console.error('数据迁移失败:', error);
    }
  }

  /**
   * 通用的存储方法
   */
  async set(key, value) {
    if (!this.db) {
      // 回退到localStorage
      return this.setLocalStorage(key, value);
    }

    try {
      const transaction = this.db.transaction([this.stores.cache], 'readwrite');
      const store = transaction.objectStore(this.stores.cache);

      await new Promise((resolve, reject) => {
        const request = store.put({
          id: key,
          value: value,
          timestamp: Date.now(),
        });

        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      return true;
    } catch (error) {
      console.error('IndexedDB存储失败，回退到localStorage:', error);
      return this.setLocalStorage(key, value);
    }
  }

  /**
   * 通用的获取方法
   */
  async get(key) {
    if (!this.db) {
      // 回退到localStorage
      return this.getLocalStorage(key);
    }

    try {
      const transaction = this.db.transaction([this.stores.cache], 'readonly');
      const store = transaction.objectStore(this.stores.cache);

      const result = await new Promise((resolve, reject) => {
        const request = store.get(key);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });

      return result ? result.value : null;
    } catch (error) {
      console.error('IndexedDB读取失败，回退到localStorage:', error);
      return this.getLocalStorage(key);
    }
  }

  /**
   * 存储书签检测结果
   */
  async setBookmarkResults(results) {
    if (!this.db) {
      return this.setLocalStorage('bookmarkResults', results);
    }

    try {
      const transaction = this.db.transaction(
        [this.stores.bookmarkResults],
        'readwrite',
      );
      const store = transaction.objectStore(this.stores.bookmarkResults);

      // 清空现有数据
      await new Promise((resolve, reject) => {
        const clearRequest = store.clear();
        clearRequest.onsuccess = () => resolve();
        clearRequest.onerror = () => reject(clearRequest.error);
      });

      // 批量插入新数据
      const promises = Object.entries(results).map(([bookmarkId, result]) => {
        return new Promise((resolve, reject) => {
          const request = store.put({
            id: bookmarkId,
            ...result,
          });
          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        });
      });

      await Promise.all(promises);
      return true;
    } catch (error) {
      console.error('存储书签结果失败:', error);
      return this.setLocalStorage('bookmarkResults', results);
    }
  }

  /**
   * 获取书签检测结果
   */
  async getBookmarkResults() {
    if (!this.db) {
      return this.getLocalStorage('bookmarkResults') || {};
    }

    try {
      const transaction = this.db.transaction(
        [this.stores.bookmarkResults],
        'readonly',
      );
      const store = transaction.objectStore(this.stores.bookmarkResults);

      const results = await new Promise((resolve, reject) => {
        const request = store.getAll();
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });

      // 转换为对象格式
      const resultsObj = {};
      results.forEach((result) => {
        const { id, ...data } = result;
        resultsObj[id] = data;
      });

      return resultsObj;
    } catch (error) {
      console.error('获取书签结果失败:', error);
      return this.getLocalStorage('bookmarkResults') || {};
    }
  }

  /**
   * 存储设置
   */
  async setSettings(settings) {
    if (!this.db) {
      return this.setLocalStorage('settings', settings);
    }

    try {
      const transaction = this.db.transaction(
        [this.stores.settings],
        'readwrite',
      );
      const store = transaction.objectStore(this.stores.settings);

      await new Promise((resolve, reject) => {
        const request = store.put({
          id: 'main',
          ...settings,
          updatedAt: Date.now(),
        });
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      return true;
    } catch (error) {
      console.error('存储设置失败:', error);
      return this.setLocalStorage('settings', settings);
    }
  }

  /**
   * 获取设置
   */
  async getSettings() {
    if (!this.db) {
      return this.getLocalStorage('settings') || {};
    }

    try {
      const transaction = this.db.transaction(
        [this.stores.settings],
        'readonly',
      );
      const store = transaction.objectStore(this.stores.settings);

      const result = await new Promise((resolve, reject) => {
        const request = store.get('main');
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });

      if (result) {
        // eslint-disable-next-line no-unused-vars
        const { id, updatedAt, ...settings } = result;
        return settings;
      }

      return {};
    } catch (error) {
      console.error('获取设置失败:', error);
      return this.getLocalStorage('settings') || {};
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats() {
    if (!this.db) {
      return this.getLocalStorageStats();
    }

    try {
      const stats = {
        type: 'IndexedDB',
        stores: {},
        totalSize: 0,
      };

      for (const storeName of Object.values(this.stores)) {
        const transaction = this.db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);

        const count = await new Promise((resolve, reject) => {
          const request = store.count();
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(request.error);
        });

        stats.stores[storeName] = { count };
      }

      return stats;
    } catch (error) {
      console.error('获取存储统计失败:', error);
      return this.getLocalStorageStats();
    }
  }

  /**
   * 清空所有数据
   */
  async clearAll() {
    if (!this.db) {
      return this.clearLocalStorage();
    }

    try {
      const promises = Object.values(this.stores).map((storeName) => {
        return new Promise((resolve, reject) => {
          const transaction = this.db.transaction([storeName], 'readwrite');
          const store = transaction.objectStore(storeName);
          const request = store.clear();
          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        });
      });

      await Promise.all(promises);
      console.log('已清空所有IndexedDB数据');
      return true;
    } catch (error) {
      console.error('清空数据失败:', error);
      return false;
    }
  }

  // localStorage回退方法
  setLocalStorage(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('localStorage存储失败:', error);
      return false;
    }
  }

  getLocalStorage(key) {
    try {
      const value = localStorage.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('localStorage读取失败:', error);
      return null;
    }
  }

  getLocalStorageStats() {
    let totalSize = 0;
    const items = {};

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      const value = localStorage.getItem(key);
      items[key] = value ? value.length : 0;
      totalSize += items[key];
    }

    return {
      type: 'localStorage',
      totalSize: totalSize,
      items: items,
    };
  }

  clearLocalStorage() {
    try {
      localStorage.clear();
      return true;
    } catch (error) {
      console.error('清空localStorage失败:', error);
      return false;
    }
  }
}

// 全局实例
window.storageManager = new StorageManager();
