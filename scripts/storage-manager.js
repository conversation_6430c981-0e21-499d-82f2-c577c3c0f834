/**
 * 存储管理器
 * 使用IndexedDB替代localStorage，支持更大数据量和更好的性能
 */
class StorageManager {
  constructor() {
    this.dbName = 'BookmarkCheckerDB';
    this.dbVersion = 1;
    this.db = null;

    this.stores = {
      bookmarkResults: 'bookmarkResults',
      settings: 'settings',
      cache: 'cache',
      scheduledCheck: 'scheduledCheck',
      incrementalCheck: 'incrementalCheck',
    };
  }

  /**
   * 初始化数据库
   */
  async init() {
    try {
      this.db = await this.openDatabase();
      console.log('IndexedDB 初始化成功');

      // 迁移现有的localStorage数据
      await this.migrateFromLocalStorage();

      return true;
    } catch (error) {
      console.error('IndexedDB 初始化失败，回退到localStorage:', error);
      return false;
    }
  }

  /**
   * 打开数据库
   */
  openDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        reject(new Error('无法打开IndexedDB'));
      };

      request.onsuccess = (event) => {
        resolve(event.target.result);
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;

        // 创建对象存储
        Object.values(this.stores).forEach((storeName) => {
          if (!db.objectStoreNames.contains(storeName)) {
            const store = db.createObjectStore(storeName, { keyPath: 'id' });

            // 为某些存储添加索引
            if (storeName === this.stores.bookmarkResults) {
              store.createIndex('status', 'status', { unique: false });
              store.createIndex('lastChecked', 'lastChecked', {
                unique: false,
              });
            }
          }
        });
      };
    });
  }

  /**
   * 从chrome.storage.local迁移数据
   */
  async migrateFromLocalStorage() {
    try {
      // 从 chrome.storage.local 迁移数据
      const stored = await chrome.storage.local.get([
        'bookmarkResults',
        'settings',
        'scheduledCheck',
        'lastIncrementalCheck',
        'bookmarkHashes',
        'cacheSettings',
      ]);

      // 迁移书签检测结果
      if (stored.bookmarkResults) {
        await this.setBookmarkResults(stored.bookmarkResults);
        await chrome.storage.local.remove(['bookmarkResults']);
        console.log('已迁移书签检测结果到IndexedDB');
      }

      // 迁移设置
      if (stored.settings) {
        await this.setSettings(stored.settings);
        await chrome.storage.local.remove(['settings']);
        console.log('已迁移设置到IndexedDB');
      }

      // 迁移其他数据
      const otherKeys = [
        'scheduledCheck',
        'lastIncrementalCheck',
        'bookmarkHashes',
        'cacheSettings',
      ];
      for (const key of otherKeys) {
        if (stored[key]) {
          await this.set(key, stored[key]);
          await chrome.storage.local.remove([key]);
        }
      }
    } catch (error) {
      console.error('数据迁移失败:', error);
    }
  }

  /**
   * 通用的存储方法
   */
  async set(key, value) {
    if (!this.db) {
      // 回退到chrome.storage.local
      return await this.setLocalStorage(key, value);
    }

    try {
      const transaction = this.db.transaction([this.stores.cache], 'readwrite');
      const store = transaction.objectStore(this.stores.cache);

      await new Promise((resolve, reject) => {
        const request = store.put({
          id: key,
          value: value,
          timestamp: Date.now(),
        });

        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      return true;
    } catch (error) {
      console.error('IndexedDB存储失败，回退到chrome.storage.local:', error);
      return await this.setLocalStorage(key, value);
    }
  }

  /**
   * 通用的获取方法
   */
  async get(key) {
    if (!this.db) {
      // 回退到chrome.storage.local
      return await this.getLocalStorage(key);
    }

    try {
      const transaction = this.db.transaction([this.stores.cache], 'readonly');
      const store = transaction.objectStore(this.stores.cache);

      const result = await new Promise((resolve, reject) => {
        const request = store.get(key);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });

      return result ? result.value : null;
    } catch (error) {
      console.error('IndexedDB读取失败，回退到chrome.storage.local:', error);
      return await this.getLocalStorage(key);
    }
  }

  /**
   * 存储书签检测结果
   */
  async setBookmarkResults(results) {
    if (!this.db) {
      return await this.setLocalStorage('bookmarkResults', results);
    }

    try {
      const transaction = this.db.transaction(
        [this.stores.bookmarkResults],
        'readwrite',
      );
      const store = transaction.objectStore(this.stores.bookmarkResults);

      // 清空现有数据
      await new Promise((resolve, reject) => {
        const clearRequest = store.clear();
        clearRequest.onsuccess = () => resolve();
        clearRequest.onerror = () => reject(clearRequest.error);
      });

      // 批量插入新数据
      const promises = Object.entries(results).map(([bookmarkId, result]) => {
        return new Promise((resolve, reject) => {
          const request = store.put({
            id: bookmarkId,
            ...result,
          });
          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        });
      });

      await Promise.all(promises);
      return true;
    } catch (error) {
      console.error('存储书签结果失败:', error);
      return await this.setLocalStorage('bookmarkResults', results);
    }
  }

  /**
   * 获取书签检测结果
   */
  async getBookmarkResults() {
    if (!this.db) {
      return (await this.getLocalStorage('bookmarkResults')) || {};
    }

    try {
      const transaction = this.db.transaction(
        [this.stores.bookmarkResults],
        'readonly',
      );
      const store = transaction.objectStore(this.stores.bookmarkResults);

      const results = await new Promise((resolve, reject) => {
        const request = store.getAll();
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });

      // 转换为对象格式
      const resultsObj = {};
      results.forEach((result) => {
        const { id, ...data } = result;
        resultsObj[id] = data;
      });

      return resultsObj;
    } catch (error) {
      console.error('获取书签结果失败:', error);
      return (await this.getLocalStorage('bookmarkResults')) || {};
    }
  }

  /**
   * 存储设置
   */
  async setSettings(settings) {
    if (!this.db) {
      return await this.setLocalStorage('settings', settings);
    }

    try {
      const transaction = this.db.transaction(
        [this.stores.settings],
        'readwrite',
      );
      const store = transaction.objectStore(this.stores.settings);

      await new Promise((resolve, reject) => {
        const request = store.put({
          id: 'main',
          ...settings,
          updatedAt: Date.now(),
        });
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      return true;
    } catch (error) {
      console.error('存储设置失败:', error);
      return await this.setLocalStorage('settings', settings);
    }
  }

  /**
   * 获取设置
   */
  async getSettings() {
    if (!this.db) {
      return (await this.getLocalStorage('settings')) || {};
    }

    try {
      const transaction = this.db.transaction(
        [this.stores.settings],
        'readonly',
      );
      const store = transaction.objectStore(this.stores.settings);

      const result = await new Promise((resolve, reject) => {
        const request = store.get('main');
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });

      if (result) {
        // eslint-disable-next-line no-unused-vars
        const { id, updatedAt, ...settings } = result;
        return settings;
      }

      return {};
    } catch (error) {
      console.error('获取设置失败:', error);
      return (await this.getLocalStorage('settings')) || {};
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats() {
    if (!this.db) {
      return await this.getLocalStorageStats();
    }

    try {
      const stats = {
        type: 'IndexedDB',
        stores: {},
        totalSize: 0,
      };

      for (const storeName of Object.values(this.stores)) {
        const transaction = this.db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);

        const count = await new Promise((resolve, reject) => {
          const request = store.count();
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(request.error);
        });

        stats.stores[storeName] = { count };
      }

      return stats;
    } catch (error) {
      console.error('获取存储统计失败:', error);
      return await this.getLocalStorageStats();
    }
  }

  /**
   * 清空所有数据
   */
  async clearAll() {
    if (!this.db) {
      return await this.clearLocalStorage();
    }

    try {
      const promises = Object.values(this.stores).map((storeName) => {
        return new Promise((resolve, reject) => {
          const transaction = this.db.transaction([storeName], 'readwrite');
          const store = transaction.objectStore(storeName);
          const request = store.clear();
          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        });
      });

      await Promise.all(promises);
      console.log('已清空所有IndexedDB数据');
      return true;
    } catch (error) {
      console.error('清空数据失败:', error);
      return false;
    }
  }

  // chrome.storage.local回退方法
  async setLocalStorage(key, value) {
    try {
      await chrome.storage.local.set({ [key]: value });
      return true;
    } catch (error) {
      console.error('chrome.storage.local存储失败:', error);
      return false;
    }
  }

  async getLocalStorage(key) {
    try {
      const result = await chrome.storage.local.get([key]);
      return result[key] || null;
    } catch (error) {
      console.error('chrome.storage.local读取失败:', error);
      return null;
    }
  }

  async getLocalStorageStats() {
    try {
      const allData = await chrome.storage.local.get(null);
      const items = {};
      let totalSize = 0;

      for (const [key, value] of Object.entries(allData)) {
        const size = JSON.stringify(value).length;
        items[key] = size;
        totalSize += size;
      }

      return {
        type: 'chrome.storage.local',
        totalSize: totalSize,
        items: items,
      };
    } catch (error) {
      console.error('获取chrome.storage.local统计失败:', error);
      return {
        type: 'chrome.storage.local',
        totalSize: 0,
        items: {},
      };
    }
  }

  async clearLocalStorage() {
    try {
      await chrome.storage.local.clear();
      return true;
    } catch (error) {
      console.error('清空chrome.storage.local失败:', error);
      return false;
    }
  }
}

// 全局实例
self.storageManager = new StorageManager();
