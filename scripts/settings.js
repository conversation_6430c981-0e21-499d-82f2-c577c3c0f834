// 设置页面脚本

class SettingsManager {
  constructor() {
    this.defaultSettings = {
      timeout: 10,
      concurrent: 5,
      retries: 2,
      autoScan: false,
      showNotifications: true,
      darkMode: false,
      cacheResults: true,
      cacheExpiryHours: 24,
      userAgent: '',
      excludePatterns: [],
    };

    this.currentSettings = { ...this.defaultSettings };
    this.scheduledSettings = {
      enabled: false,
      interval: 'weekly',
      customMinutes: 60,
      onlyInvalid: false,
      maxConcurrent: 5,
    };
    this.init();
  }

  async init() {
    await this.loadSettings();
    await this.loadScheduledSettings();
    this.bindEvents();
    this.updateUI();
    this.updateScheduledUI();
    this.applyTheme();
    this.updateScheduleStatus();
  }

  bindEvents() {
    // 返回按钮
    document.getElementById('backBtn').addEventListener('click', () => {
      window.close();
    });

    // 保存按钮
    document.getElementById('saveBtn').addEventListener('click', () => {
      this.saveSettings();
    });

    // 重置按钮
    document.getElementById('resetBtn').addEventListener('click', () => {
      this.resetSettings();
    });

    // 导出设置
    document
      .getElementById('exportSettingsBtn')
      .addEventListener('click', () => {
        this.exportSettings();
      });

    // 导入设置
    document
      .getElementById('importSettingsBtn')
      .addEventListener('click', () => {
        document.getElementById('importFileInput').click();
      });

    document
      .getElementById('importFileInput')
      .addEventListener('change', (e) => {
        this.importSettings(e.target.files[0]);
      });

    // 深色模式切换
    document
      .getElementById('darkModeSetting')
      .addEventListener('change', (e) => {
        this.currentSettings.darkMode = e.target.checked;
        this.applyTheme();
      });

    // 缓存设置联动
    document
      .getElementById('cacheResultsSetting')
      .addEventListener('change', (e) => {
        const cacheExpiryInput = document.getElementById('cacheExpiryHours');
        cacheExpiryInput
          .closest('.setting-item')
          .classList.toggle('disabled', !e.target.checked);
      });

    // 实时验证输入
    this.bindValidation();

    // 定时检测相关事件
    this.bindScheduledEvents();
  }

  bindValidation() {
    // 超时时间验证
    document.getElementById('timeoutSetting').addEventListener('input', (e) => {
      const value = parseInt(e.target.value);
      if (value < 1 || value > 60) {
        e.target.setCustomValidity('超时时间必须在1-60秒之间');
      } else {
        e.target.setCustomValidity('');
      }
    });

    // 并发数量验证
    document
      .getElementById('concurrentSetting')
      .addEventListener('input', (e) => {
        const value = parseInt(e.target.value);
        if (value < 1 || value > 20) {
          e.target.setCustomValidity('并发数量必须在1-20之间');
        } else {
          e.target.setCustomValidity('');
        }
      });
  }

  bindScheduledEvents() {
    // 启用/禁用定时检测
    document
      .getElementById('enableScheduled')
      .addEventListener('change', async(e) => {
        const enabled = e.target.checked;
        this.scheduledSettings.enabled = enabled;

        if (enabled) {
          await this.enableScheduledCheck();
        } else {
          await this.disableScheduledCheck();
        }

        this.updateScheduledOptionsVisibility();
        this.updateScheduleStatus();
      });

    // 检测频率变化
    document.getElementById('checkInterval').addEventListener('change', (e) => {
      this.scheduledSettings.interval = e.target.value;
      this.updateCustomIntervalVisibility();

      if (this.scheduledSettings.enabled) {
        this.enableScheduledCheck();
      }
    });

    // 自定义间隔变化
    document.getElementById('customMinutes').addEventListener('change', (e) => {
      this.scheduledSettings.customMinutes = parseInt(e.target.value);

      if (
        this.scheduledSettings.enabled &&
        this.scheduledSettings.interval === 'custom'
      ) {
        this.enableScheduledCheck();
      }
    });

    // 只检测失效书签
    document.getElementById('onlyInvalid').addEventListener('change', (e) => {
      this.scheduledSettings.onlyInvalid = e.target.checked;
      this.saveScheduledSettings();
    });

    // 立即运行
    document.getElementById('runNowBtn').addEventListener('click', async() => {
      await this.runScheduledCheckNow();
    });

    // 导入导出相关事件
    this.bindImportExportEvents();

    // 存储管理相关事件
    this.bindStorageEvents();
  }

  bindImportExportEvents() {
    // 导出按钮
    document.getElementById('exportJsonBtn')?.addEventListener('click', () => {
      this.exportBookmarks('json');
    });

    document.getElementById('exportCsvBtn')?.addEventListener('click', () => {
      this.exportBookmarks('csv');
    });

    document.getElementById('exportHtmlBtn')?.addEventListener('click', () => {
      this.exportBookmarks('html');
    });

    // 导入按钮
    document.getElementById('importBtn')?.addEventListener('click', () => {
      const fileInput = document.getElementById('importFile');
      const file = fileInput.files[0];

      if (file) {
        this.importBookmarks(file);
      } else {
        this.showMessage('请选择要导入的文件', 'error');
      }
    });

    // 文件选择变化
    document.getElementById('importFile')?.addEventListener('change', (e) => {
      const file = e.target.files[0];
      const importBtn = document.getElementById('importBtn');

      if (file && importBtn) {
        importBtn.textContent = `导入 ${file.name}`;
        importBtn.disabled = false;
      }
    });
  }

  bindStorageEvents() {
    // 清除缓存按钮
    document
      .getElementById('clearCacheBtn')
      ?.addEventListener('click', async() => {
        if (confirm('确定要清除所有缓存数据吗？这将删除所有检测结果。')) {
          await this.clearCache();
        }
      });

    // 清空所有数据按钮
    document
      .getElementById('clearAllStorageBtn')
      ?.addEventListener('click', async() => {
        if (
          confirm(
            '确定要清空所有数据吗？这将删除所有设置、检测结果和缓存数据，此操作不可恢复！',
          )
        ) {
          await this.clearAllStorage();
        }
      });

    // 定期更新存储统计
    this.updateStorageStats();
    setInterval(() => {
      this.updateStorageStats();
    }, 30000); // 每30秒更新一次
  }

  async loadSettings() {
    try {
      const stored = await chrome.storage.local.get(['settings']);
      if (stored.settings) {
        this.currentSettings = { ...this.defaultSettings, ...stored.settings };
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      this.showMessage('加载设置失败', 'error');
    }
  }

  async saveSettings() {
    try {
      // 收集表单数据
      this.currentSettings = {
        timeout: parseInt(document.getElementById('timeoutSetting').value),
        concurrent: parseInt(
          document.getElementById('concurrentSetting').value,
        ),
        retries: parseInt(document.getElementById('retriesSetting').value),
        autoScan: document.getElementById('autoScanSetting').checked,
        showNotifications: document.getElementById('showNotificationsSetting')
          .checked,
        darkMode: document.getElementById('darkModeSetting').checked,
        cacheResults: document.getElementById('cacheResultsSetting').checked,
        cacheExpiryHours: parseInt(
          document.getElementById('cacheExpiryHours').value,
        ),
        userAgent: document.getElementById('userAgentSetting').value.trim(),
        excludePatterns: this.parseExcludePatterns(),
      };

      // 验证设置
      if (!this.validateSettings()) {
        return;
      }

      // 保存到存储
      await chrome.storage.local.set({ settings: this.currentSettings });

      // 通知后台脚本更新设置
      await chrome.runtime.sendMessage({
        action: 'updateSettings',
        settings: this.currentSettings,
      });

      this.showMessage('设置已保存', 'success');
    } catch (error) {
      console.error('Error saving settings:', error);
      this.showMessage('保存设置失败', 'error');
    }
  }

  validateSettings() {
    const timeout = this.currentSettings.timeout;
    const concurrent = this.currentSettings.concurrent;
    const retries = this.currentSettings.retries;
    const cacheExpiry = this.currentSettings.cacheExpiryHours;

    if (timeout < 1 || timeout > 60) {
      this.showMessage('超时时间必须在1-60秒之间', 'error');
      return false;
    }

    if (concurrent < 1 || concurrent > 20) {
      this.showMessage('并发数量必须在1-20之间', 'error');
      return false;
    }

    if (retries < 0 || retries > 5) {
      this.showMessage('重试次数必须在0-5之间', 'error');
      return false;
    }

    if (cacheExpiry < 1 || cacheExpiry > 168) {
      this.showMessage('缓存有效期必须在1-168小时之间', 'error');
      return false;
    }

    return true;
  }

  parseExcludePatterns() {
    const text = document.getElementById('excludePatternsSetting').value;
    return text
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line.length > 0);
  }

  resetSettings() {
    if (confirm('确定要重置所有设置为默认值吗？')) {
      this.currentSettings = { ...this.defaultSettings };
      this.updateUI();
      this.applyTheme();
      this.showMessage('设置已重置为默认值', 'success');
    }
  }

  updateUI() {
    document.getElementById('timeoutSetting').value =
      this.currentSettings.timeout;
    document.getElementById('concurrentSetting').value =
      this.currentSettings.concurrent;
    document.getElementById('retriesSetting').value =
      this.currentSettings.retries;
    document.getElementById('autoScanSetting').checked =
      this.currentSettings.autoScan;
    document.getElementById('showNotificationsSetting').checked =
      this.currentSettings.showNotifications;
    document.getElementById('darkModeSetting').checked =
      this.currentSettings.darkMode;
    document.getElementById('cacheResultsSetting').checked =
      this.currentSettings.cacheResults;
    document.getElementById('cacheExpiryHours').value =
      this.currentSettings.cacheExpiryHours;
    document.getElementById('userAgentSetting').value =
      this.currentSettings.userAgent;
    document.getElementById('excludePatternsSetting').value =
      this.currentSettings.excludePatterns.join('\n');

    // 更新缓存设置状态
    const cacheExpiryInput = document.getElementById('cacheExpiryHours');
    cacheExpiryInput
      .closest('.setting-item')
      .classList.toggle('disabled', !this.currentSettings.cacheResults);
  }

  applyTheme() {
    document.body.classList.toggle('dark-mode', this.currentSettings.darkMode);
  }

  exportSettings() {
    const dataStr = JSON.stringify(this.currentSettings, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `bookmark-checker-settings-${
      new Date().toISOString().split('T')[0]
    }.json`;
    a.click();

    URL.revokeObjectURL(url);
    this.showMessage('设置已导出', 'success');
  }

  async importSettings(file) {
    if (!file) {
      return;
    }

    try {
      const text = await file.text();
      const importedSettings = JSON.parse(text);

      // 验证导入的设置
      const validSettings = {};
      for (const key in this.defaultSettings) {
        if (Object.prototype.hasOwnProperty.call(importedSettings, key)) {
          validSettings[key] = importedSettings[key];
        } else {
          validSettings[key] = this.defaultSettings[key];
        }
      }

      this.currentSettings = validSettings;
      this.updateUI();
      this.applyTheme();
      this.showMessage('设置已导入', 'success');
    } catch (error) {
      console.error('Error importing settings:', error);
      this.showMessage('导入设置失败：文件格式错误', 'error');
    }
  }

  showMessage(text, type = 'success') {
    // 移除现有消息
    const existingMessage = document.querySelector(
      '.success-message, .error-message',
    );
    if (existingMessage) {
      existingMessage.remove();
    }

    // 创建新消息
    const message = document.createElement('div');
    message.className =
      type === 'success' ? 'success-message' : 'error-message';
    message.textContent = text;
    message.classList.add('fade-in');

    // 插入到设置内容顶部
    const settingsContent = document.querySelector('.settings-content');
    settingsContent.insertBefore(message, settingsContent.firstChild);

    // 3秒后自动移除
    setTimeout(() => {
      if (message.parentNode) {
        message.remove();
      }
    }, 3000);
  }

  // 定时检测相关方法
  async loadScheduledSettings() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'getScheduledCheckSettings',
      });
      if (response.success) {
        this.scheduledSettings = {
          ...this.scheduledSettings,
          ...response.data,
        };
      }
    } catch (error) {
      console.error('Error loading scheduled settings:', error);
    }
  }

  async saveScheduledSettings() {
    try {
      await chrome.runtime.sendMessage({
        action: 'saveScheduledCheckSettings',
        settings: this.scheduledSettings,
      });
    } catch (error) {
      console.error('Error saving scheduled settings:', error);
    }
  }

  async enableScheduledCheck() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'enableScheduledCheck',
        interval: this.scheduledSettings.interval,
        customMinutes: this.scheduledSettings.customMinutes,
      });

      if (response.success) {
        this.showMessage('定时检测已启用', 'success');
      } else {
        this.showMessage('启用定时检测失败', 'error');
      }
    } catch (error) {
      console.error('Error enabling scheduled check:', error);
      this.showMessage('启用定时检测失败', 'error');
    }
  }

  async disableScheduledCheck() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'disableScheduledCheck',
      });

      if (response.success) {
        this.showMessage('定时检测已禁用', 'success');
      } else {
        this.showMessage('禁用定时检测失败', 'error');
      }
    } catch (error) {
      console.error('Error disabling scheduled check:', error);
      this.showMessage('禁用定时检测失败', 'error');
    }
  }

  async runScheduledCheckNow() {
    try {
      const button = document.getElementById('runNowBtn');
      button.disabled = true;
      button.innerHTML =
        '<span class="material-icons">hourglass_empty</span>运行中...';

      await chrome.runtime.sendMessage({
        action: 'runScheduledCheckNow',
      });

      this.showMessage('定时检测已开始执行', 'success');

      // 恢复按钮状态
      setTimeout(() => {
        button.disabled = false;
        button.innerHTML =
          '<span class="material-icons">play_arrow</span>立即运行一次';
      }, 2000);
    } catch (error) {
      console.error('Error running scheduled check:', error);
      this.showMessage('执行定时检测失败', 'error');
    }
  }

  async updateScheduleStatus() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'getScheduledCheckStatus',
      });

      if (response.success) {
        const status = response.data;

        document.getElementById('statusText').textContent = status.enabled
          ? '已启用'
          : '未启用';

        document.getElementById('lastRunText').textContent = status.lastRun
          ? new Date(status.lastRun).toLocaleString()
          : '从未';

        document.getElementById('nextRunText').textContent = status.nextRun
          ? new Date(status.nextRun).toLocaleString()
          : '-';
      }
    } catch (error) {
      console.error('Error updating schedule status:', error);
    }
  }

  updateScheduledUI() {
    document.getElementById('enableScheduled').checked =
      this.scheduledSettings.enabled;
    document.getElementById('checkInterval').value =
      this.scheduledSettings.interval;
    document.getElementById('customMinutes').value =
      this.scheduledSettings.customMinutes;
    document.getElementById('onlyInvalid').checked =
      this.scheduledSettings.onlyInvalid;

    this.updateScheduledOptionsVisibility();
    this.updateCustomIntervalVisibility();
  }

  updateScheduledOptionsVisibility() {
    const options = document.getElementById('scheduledOptions');
    const runNowBtn = document.getElementById('runNowBtn');
    const enabled = this.scheduledSettings.enabled;

    options.style.display = enabled ? 'block' : 'none';
    runNowBtn.style.display = enabled ? 'block' : 'none';
  }

  updateCustomIntervalVisibility() {
    const customGroup = document.getElementById('customIntervalGroup');
    const isCustom = this.scheduledSettings.interval === 'custom';

    customGroup.style.display = isCustom ? 'block' : 'none';
  }

  // 导入导出方法
  async exportBookmarks(format) {
    try {
      const button = document.querySelector(
        `#export${format.charAt(0).toUpperCase() + format.slice(1)}Btn`,
      );
      if (button) {
        button.disabled = true;
        button.innerHTML =
          '<span class="material-icons">hourglass_empty</span>导出中...';
      }

      const response = await chrome.runtime.sendMessage({
        action: 'exportBookmarks',
        format: format,
        options: {
          includeResults: true,
          includeTimestamp: true,
        },
      });

      if (response.success) {
        this.showMessage(
          `成功导出 ${response.count} 个书签到 ${response.filename}`,
          'success',
        );
      } else {
        this.showMessage(`导出失败: ${response.error}`, 'error');
      }
    } catch (error) {
      console.error('导出书签失败:', error);
      this.showMessage('导出书签失败', 'error');
    } finally {
      // 恢复按钮状态
      setTimeout(() => {
        const button = document.querySelector(
          `#export${format.charAt(0).toUpperCase() + format.slice(1)}Btn`,
        );
        if (button) {
          button.disabled = false;
          button.innerHTML = `<span class="material-icons">download</span>导出为${format.toUpperCase()}`;
        }
      }, 2000);
    }
  }

  async importBookmarks(file) {
    try {
      const importBtn = document.getElementById('importBtn');
      if (importBtn) {
        importBtn.disabled = true;
        importBtn.innerHTML =
          '<span class="material-icons">hourglass_empty</span>导入中...';
      }

      // 由于文件处理需要在popup环境中进行，我们使用import-export.js
      const result = await window.importExportManager.importBookmarks(file);

      if (result.success) {
        this.showMessage(
          `导入完成: 成功导入 ${result.imported} 个书签，跳过 ${result.skipped} 个重复书签`,
          'success',
        );

        if (result.errors.length > 0) {
          console.warn('导入过程中的错误:', result.errors);
        }
      } else {
        this.showMessage(`导入失败: ${result.error}`, 'error');
      }
    } catch (error) {
      console.error('导入书签失败:', error);
      this.showMessage('导入书签失败', 'error');
    } finally {
      // 恢复按钮状态
      setTimeout(() => {
        const importBtn = document.getElementById('importBtn');
        const fileInput = document.getElementById('importFile');

        if (importBtn) {
          importBtn.disabled = false;
          importBtn.innerHTML =
            '<span class="material-icons">upload</span>导入书签';
        }

        if (fileInput) {
          fileInput.value = '';
        }
      }, 2000);
    }
  }

  // 存储管理方法
  async updateStorageStats() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'getStorageStats',
      });

      if (response.success) {
        const stats = response.data;

        document.getElementById('storageType').textContent =
          stats.type || 'localStorage';

        if (stats.type === 'IndexedDB' && stats.stores) {
          const bookmarkCount = stats.stores.bookmarkResults?.count || 0;
          document.getElementById('bookmarkCount').textContent = bookmarkCount;

          // 计算总大小（估算）
          let totalItems = 0;
          Object.values(stats.stores).forEach((store) => {
            totalItems += store.count || 0;
          });
          document.getElementById('cacheSize').textContent =
            `${totalItems} 条记录`;
        } else if (stats.items) {
          // localStorage统计
          const itemCount = Object.keys(stats.items).length;
          const sizeKB = Math.round(stats.totalSize / 1024);
          document.getElementById('bookmarkCount').textContent = itemCount;
          document.getElementById('cacheSize').textContent = `${sizeKB} KB`;
        }
      }
    } catch (error) {
      console.error('更新存储统计失败:', error);
    }
  }

  async clearCache() {
    try {
      const button = document.getElementById('clearCacheBtn');
      if (button) {
        button.disabled = true;
        button.innerHTML =
          '<span class="material-icons">hourglass_empty</span>清除中...';
      }

      const response = await chrome.runtime.sendMessage({
        action: 'clearCache',
      });

      if (response.success) {
        this.showMessage('缓存已清除', 'success');
        this.updateStorageStats();
      } else {
        this.showMessage('清除缓存失败', 'error');
      }
    } catch (error) {
      console.error('清除缓存失败:', error);
      this.showMessage('清除缓存失败', 'error');
    } finally {
      // 恢复按钮状态
      setTimeout(() => {
        const button = document.getElementById('clearCacheBtn');
        if (button) {
          button.disabled = false;
          button.innerHTML =
            '<span class="material-icons">clear_all</span>清除缓存';
        }
      }, 2000);
    }
  }

  async clearAllStorage() {
    try {
      const button = document.getElementById('clearAllStorageBtn');
      if (button) {
        button.disabled = true;
        button.innerHTML =
          '<span class="material-icons">hourglass_empty</span>清除中...';
      }

      const response = await chrome.runtime.sendMessage({
        action: 'clearAllStorage',
      });

      if (response.success) {
        this.showMessage('所有数据已清空', 'success');
        this.updateStorageStats();

        // 重置设置为默认值
        setTimeout(() => {
          this.resetSettings();
        }, 1000);
      } else {
        this.showMessage('清空数据失败', 'error');
      }
    } catch (error) {
      console.error('清空数据失败:', error);
      this.showMessage('清空数据失败', 'error');
    } finally {
      // 恢复按钮状态
      setTimeout(() => {
        const button = document.getElementById('clearAllStorageBtn');
        if (button) {
          button.disabled = false;
          button.innerHTML =
            '<span class="material-icons">delete_forever</span>清空所有数据';
        }
      }, 2000);
    }
  }
}

// 初始化设置管理器
document.addEventListener('DOMContentLoaded', () => {
  new SettingsManager();
});
