<!DOCTYPE html>
<html>
<head>
    <title>生成Chrome扩展图标</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .icon-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .icon-item { text-align: center; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        canvas { border: 2px solid #007bff; border-radius: 4px; }
        .download-btn { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px; }
        .download-btn:hover { background: #0056b3; }
        .instructions { background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔖 Chrome扩展图标生成器</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ol>
                <li>点击下方的"下载"按钮保存每个尺寸的图标</li>
                <li>将下载的文件重命名为对应的文件名（如icon16.png）</li>
                <li>将所有图标文件放入扩展的icons目录中</li>
            </ol>
        </div>

        <div class="icon-grid">
            <div class="icon-item">
                <h3>16×16</h3>
                <canvas id="icon16" width="16" height="16"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon('icon16', 'icon16.png')">下载 icon16.png</button>
            </div>
            
            <div class="icon-item">
                <h3>32×32</h3>
                <canvas id="icon32" width="32" height="32"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon('icon32', 'icon32.png')">下载 icon32.png</button>
            </div>
            
            <div class="icon-item">
                <h3>48×48</h3>
                <canvas id="icon48" width="48" height="48"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon('icon48', 'icon48.png')">下载 icon48.png</button>
            </div>
            
            <div class="icon-item">
                <h3>128×128</h3>
                <canvas id="icon128" width="128" height="128"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon('icon128', 'icon128.png')">下载 icon128.png</button>
            </div>
        </div>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 128;
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#007bff');
            gradient.addColorStop(1, '#0056b3');
            
            // 背景圆形
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 2*scale, 0, 2 * Math.PI);
            ctx.fill();
            
            // 书签图标
            const bookmarkWidth = 40 * scale;
            const bookmarkHeight = 50 * scale;
            const bookmarkX = (size - bookmarkWidth) / 2;
            const bookmarkY = 25 * scale;
            
            // 书签主体
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(bookmarkX, bookmarkY, bookmarkWidth, bookmarkHeight);
            
            // 书签底部V形切口
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.moveTo(bookmarkX + bookmarkWidth/3, bookmarkY + bookmarkHeight);
            ctx.lineTo(bookmarkX + bookmarkWidth/2, bookmarkY + bookmarkHeight - 8*scale);
            ctx.lineTo(bookmarkX + 2*bookmarkWidth/3, bookmarkY + bookmarkHeight);
            ctx.fill();
            
            // 检查标记
            ctx.strokeStyle = '#28a745';
            ctx.lineWidth = Math.max(2 * scale, 1);
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            ctx.beginPath();
            ctx.moveTo(bookmarkX + 8*scale, bookmarkY + 20*scale);
            ctx.lineTo(bookmarkX + 15*scale, bookmarkY + 27*scale);
            ctx.lineTo(bookmarkX + 32*scale, bookmarkY + 10*scale);
            ctx.stroke();
            
            // 搜索图标
            if (size >= 32) {
                ctx.strokeStyle = '#ffc107';
                ctx.lineWidth = Math.max(2 * scale, 1);
                
                // 放大镜圆圈
                ctx.beginPath();
                ctx.arc(bookmarkX + 28*scale, bookmarkY + 35*scale, 6*scale, 0, 2 * Math.PI);
                ctx.stroke();
                
                // 放大镜手柄
                ctx.beginPath();
                ctx.moveTo(bookmarkX + 32*scale, bookmarkY + 39*scale);
                ctx.lineTo(bookmarkX + 36*scale, bookmarkY + 43*scale);
                ctx.stroke();
            }
        }
        
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // 生成所有图标
        window.onload = function() {
            [16, 32, 48, 128].forEach(size => {
                const canvas = document.getElementById('icon' + size);
                drawIcon(canvas, size);
            });
        };
    </script>
</body>
</html>
