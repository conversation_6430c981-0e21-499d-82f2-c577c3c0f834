// 背景脚本 - 处理书签检测和数据管理

class BookmarkChecker {
  constructor() {
    this.isChecking = false;
    this.checkQueue = [];
    this.results = new Map();
    this.settings = {
      timeout: 10000, // 10秒超时
      concurrent: 5, // 并发检测数量
      retries: 2, // 重试次数
    };

    this.init();
  }

  async init() {
    // 加载设置
    const stored = await chrome.storage.local.get(['settings', 'results']);
    if (stored.settings) {
      this.settings = { ...this.settings, ...stored.settings };
    }
    if (stored.results) {
      this.results = new Map(stored.results);
    }

    // 监听消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放
    });

    console.log('BookmarkChecker initialized');
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.action) {
        case 'getBookmarks':
          const bookmarks = await this.getAllBookmarks();
          sendResponse({ success: true, data: bookmarks });
          break;

        case 'checkBookmarks':
          await this.startChecking(message.bookmarks);
          sendResponse({ success: true });
          break;

        case 'checkSingleBookmark':
          const result = await this.checkSingleUrl(message.url);
          sendResponse({ success: true, data: result });
          break;

        case 'getResults':
          sendResponse({
            success: true,
            data: Array.from(this.results.entries()),
          });
          break;

        case 'deleteBookmark':
          await chrome.bookmarks.remove(message.id);
          sendResponse({ success: true });
          break;

        case 'updateSettings':
          this.settings = { ...this.settings, ...message.settings };
          await chrome.storage.local.set({ settings: this.settings });
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async getAllBookmarks() {
    const bookmarkTree = await chrome.bookmarks.getTree();
    const bookmarks = [];

    const traverse = (nodes) => {
      for (const node of nodes) {
        if (node.url) {
          bookmarks.push({
            id: node.id,
            title: node.title,
            url: node.url,
            parentId: node.parentId,
            dateAdded: node.dateAdded,
          });
        }
        if (node.children) {
          traverse(node.children);
        }
      }
    };

    traverse(bookmarkTree);
    return bookmarks;
  }

  async startChecking(bookmarks) {
    if (this.isChecking) {
      console.log('Already checking bookmarks');
      return;
    }

    this.isChecking = true;
    this.checkQueue = [...bookmarks];

    console.log(`Starting to check ${bookmarks.length} bookmarks`);

    // 并发检测
    const workers = [];
    for (let i = 0; i < this.settings.concurrent; i++) {
      workers.push(this.worker());
    }

    await Promise.all(workers);

    // 保存结果
    await chrome.storage.local.set({
      results: Array.from(this.results.entries()),
    });

    this.isChecking = false;
    console.log('Bookmark checking completed');

    // 显示完成通知
    await this.showCompletionNotification(bookmarks.length);

    // 通知popup更新
    this.notifyPopup('checkingComplete');
  }

  async worker() {
    while (this.checkQueue.length > 0 && this.isChecking) {
      const bookmark = this.checkQueue.shift();
      if (!bookmark) {break;}

      try {
        const result = await this.checkSingleUrl(bookmark.url);
        this.results.set(bookmark.id, {
          ...result,
          lastChecked: Date.now(),
          bookmark: bookmark,
        });

        // 通知popup更新单个结果
        this.notifyPopup('bookmarkChecked', {
          id: bookmark.id,
          result: result,
        });
      } catch (error) {
        console.error(`Error checking ${bookmark.url}:`, error);
        this.results.set(bookmark.id, {
          status: 'error',
          error: error.message,
          lastChecked: Date.now(),
          bookmark: bookmark,
        });
      }
    }
  }

  async checkSingleUrl(url) {
    const startTime = Date.now();
    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      this.settings.timeout,
    );

    try {
      // 首先尝试HEAD请求
      const response = await this.makeRequest(url, 'HEAD', controller.signal);
      clearTimeout(timeoutId);

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return {
          status: 'valid',
          statusCode: response.status,
          responseTime: responseTime,
          method: 'HEAD',
        };
      } else {
        return {
          status: 'invalid',
          statusCode: response.status,
          error: `HTTP ${response.status} ${response.statusText}`,
          responseTime: responseTime,
        };
      }
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        return {
          status: 'timeout',
          error: 'Request timeout',
          responseTime: Date.now() - startTime,
        };
      }

      // 如果HEAD请求失败，尝试GET请求
      try {
        const getController = new AbortController();
        const getTimeoutId = setTimeout(
          () => getController.abort(),
          this.settings.timeout,
        );

        const getResponse = await this.makeRequest(
          url,
          'GET',
          getController.signal,
        );
        clearTimeout(getTimeoutId);

        const responseTime = Date.now() - startTime;

        if (getResponse.ok) {
          return {
            status: 'valid',
            statusCode: getResponse.status,
            responseTime: responseTime,
            method: 'GET',
          };
        } else {
          return {
            status: 'invalid',
            statusCode: getResponse.status,
            error: `HTTP ${getResponse.status} ${getResponse.statusText}`,
            responseTime: responseTime,
          };
        }
      } catch (getError) {
        return {
          status: 'invalid',
          error: this.getErrorMessage(getError),
          responseTime: Date.now() - startTime,
        };
      }
    }
  }

  async makeRequest(url, method, signal) {
    // 使用content script来绕过CORS限制
    return new Promise((resolve, reject) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs.length === 0) {
          // 如果没有活动标签页，直接使用fetch
          fetch(url, { method, signal, mode: 'no-cors' })
            .then(resolve)
            .catch(reject);
          return;
        }

        // 尝试注入content script进行检测
        chrome.scripting.executeScript(
          {
            target: { tabId: tabs[0].id },
            func: this.checkUrlInContentScript,
            args: [url, method],
          },
          (results) => {
            if (chrome.runtime.lastError || !results || !results[0]) {
              // 如果注入失败，回退到普通fetch
              fetch(url, { method, signal, mode: 'no-cors' })
                .then(resolve)
                .catch(reject);
            } else {
              const result = results[0].result;
              if (result.success) {
                resolve({
                  ok: result.ok,
                  status: result.status,
                  statusText: result.statusText,
                });
              } else {
                reject(new Error(result.error));
              }
            }
          },
        );
      });
    });
  }

  checkUrlInContentScript(url, method) {
    return new Promise((resolve) => {
      fetch(url, { method })
        .then((response) => {
          resolve({
            success: true,
            ok: response.ok,
            status: response.status,
            statusText: response.statusText,
          });
        })
        .catch((error) => {
          resolve({
            success: false,
            error: error.message,
          });
        });
    });
  }

  getErrorMessage(error) {
    if (
      error.name === 'TypeError' &&
      error.message.includes('Failed to fetch')
    ) {
      return 'Network error or CORS blocked';
    }
    if (error.name === 'AbortError') {
      return 'Request timeout';
    }
    return error.message || 'Unknown error';
  }

  async showCompletionNotification(totalBookmarks) {
    const settings = await chrome.storage.local.get(['settings']);
    if (!settings.settings?.showNotifications) {return;}

    const validCount = Array.from(this.results.values()).filter(
      (r) => r.status === 'valid',
    ).length;
    const invalidCount = Array.from(this.results.values()).filter(
      (r) => r.status === 'invalid' || r.status === 'error',
    ).length;

    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: '书签检测完成',
      message: `检测了 ${totalBookmarks} 个书签\n有效: ${validCount}, 失效: ${invalidCount}`,
    });
  }

  notifyPopup(type, data = null) {
    // 尝试向popup发送消息
    chrome.runtime
      .sendMessage({
        type: type,
        data: data,
      })
      .catch(() => {
        // popup可能未打开，忽略错误
      });
  }
}

// 初始化书签检测器
new BookmarkChecker();
