// 背景脚本 - 处理书签检测和数据管理

class BookmarkChecker {
  constructor() {
    this.isChecking = false;
    this.checkQueue = [];
    this.results = new Map();
    this.settings = {
      timeout: 10000, // 10秒超时
      concurrent: 5, // 并发检测数量
      retries: 2, // 重试次数
    };

    this.init();
  }

  async init() {
    // 加载设置
    const stored = await chrome.storage.local.get(['settings', 'results']);
    if (stored.settings) {
      this.settings = { ...this.settings, ...stored.settings };
    }
    if (stored.results) {
      this.results = new Map(stored.results);
    }

    // 监听消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放
    });

    console.log('BookmarkChecker initialized');
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.action) {
        case 'getBookmarks':
          const bookmarks = await this.getAllBookmarks();
          sendResponse({ success: true, data: bookmarks });
          break;

        case 'checkBookmarks':
          await this.startChecking(message.bookmarks);
          sendResponse({ success: true });
          break;

        case 'checkSingleBookmark':
          const result = await this.checkSingleUrl(message.url);
          sendResponse({ success: true, data: result });
          break;

        case 'getResults':
          sendResponse({
            success: true,
            data: Array.from(this.results.entries()),
          });
          break;

        case 'deleteBookmark':
          await chrome.bookmarks.remove(message.id);
          sendResponse({ success: true });
          break;

        case 'updateSettings':
          this.settings = { ...this.settings, ...message.settings };
          await chrome.storage.local.set({ settings: this.settings });
          sendResponse({ success: true });
          break;

        case 'updateBookmarkUrl':
          await chrome.bookmarks.update(message.id, { url: message.newUrl });
          sendResponse({ success: true });
          break;

        case 'getRedirectSuggestions':
          const suggestions = this.getRedirectSuggestions();
          sendResponse({ success: true, data: suggestions });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async getAllBookmarks() {
    const bookmarkTree = await chrome.bookmarks.getTree();
    const bookmarks = [];

    const traverse = (nodes) => {
      for (const node of nodes) {
        if (node.url) {
          bookmarks.push({
            id: node.id,
            title: node.title,
            url: node.url,
            parentId: node.parentId,
            dateAdded: node.dateAdded,
          });
        }
        if (node.children) {
          traverse(node.children);
        }
      }
    };

    traverse(bookmarkTree);
    return bookmarks;
  }

  async startChecking(bookmarks) {
    if (this.isChecking) {
      console.log('Already checking bookmarks');
      return;
    }

    this.isChecking = true;
    this.checkQueue = [...bookmarks];

    console.log(`Starting to check ${bookmarks.length} bookmarks`);

    // 并发检测
    const workers = [];
    for (let i = 0; i < this.settings.concurrent; i++) {
      workers.push(this.worker());
    }

    await Promise.all(workers);

    // 保存结果
    await chrome.storage.local.set({
      results: Array.from(this.results.entries()),
    });

    this.isChecking = false;
    console.log('Bookmark checking completed');

    // 显示完成通知
    await this.showCompletionNotification(bookmarks.length);

    // 通知popup更新
    this.notifyPopup('checkingComplete');
  }

  async worker() {
    while (this.checkQueue.length > 0 && this.isChecking) {
      const bookmark = this.checkQueue.shift();
      if (!bookmark) {
        break;
      }

      try {
        const result = await this.checkSingleUrl(bookmark.url);
        this.results.set(bookmark.id, {
          ...result,
          lastChecked: Date.now(),
          bookmark: bookmark,
        });

        // 通知popup更新单个结果
        this.notifyPopup('bookmarkChecked', {
          id: bookmark.id,
          result: result,
        });
      } catch (error) {
        console.error(`Error checking ${bookmark.url}:`, error);
        this.results.set(bookmark.id, {
          status: 'error',
          error: error.message,
          lastChecked: Date.now(),
          bookmark: bookmark,
        });
      }
    }
  }

  async checkSingleUrl(url) {
    const startTime = Date.now();

    try {
      // 使用多种方法检测URL
      const result = await this.comprehensiveUrlCheck(url);
      return {
        ...result,
        responseTime: Date.now() - startTime,
        lastChecked: Date.now(),
      };
    } catch (error) {
      return {
        status: 'error',
        error: this.getErrorMessage(error),
        responseTime: Date.now() - startTime,
        lastChecked: Date.now(),
      };
    }
  }

  async comprehensiveUrlCheck(url) {
    // 1. 首先进行URL格式验证
    const urlValidation = this.validateUrl(url);
    if (!urlValidation.valid) {
      return {
        status: 'invalid',
        error: urlValidation.error,
        method: 'validation',
      };
    }

    // 2. 尝试多种检测方法（仅使用可靠的 fetch 方法）
    const methods = [
      () => this.checkWithFetch(url),
      () => this.checkWithAlternativeFetch(url),
      () => this.checkWithSimpleFetch(url),
    ];

    let lastError = null;

    for (const method of methods) {
      try {
        const result = await method();
        if (result.status === 'valid') {
          return result;
        }
        lastError = result;
      } catch (error) {
        lastError = {
          status: 'error',
          error: this.getErrorMessage(error),
        };
      }
    }

    return (
      lastError || {
        status: 'error',
        error: 'All detection methods failed',
      }
    );
  }

  validateUrl(url) {
    try {
      const urlObj = new URL(url);

      // 检查协议
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return {
          valid: false,
          error: `Unsupported protocol: ${urlObj.protocol}`,
        };
      }

      // 检查主机名
      if (!urlObj.hostname || urlObj.hostname.length === 0) {
        return {
          valid: false,
          error: 'Invalid hostname',
        };
      }

      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        error: 'Invalid URL format',
      };
    }
  }

  async checkWithFetch(url) {
    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      this.settings.timeout,
    );

    try {
      // 使用手动重定向跟踪来获取最终URL
      const redirectInfo = await this.trackRedirects(url, controller.signal);
      clearTimeout(timeoutId);

      return this.analyzeResponse(redirectInfo.response, 'fetch', redirectInfo);
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  async trackRedirects(originalUrl, signal, maxRedirects = 10) {
    let currentUrl = originalUrl;
    const redirectChain = [];
    let redirectCount = 0;

    while (redirectCount < maxRedirects) {
      try {
        // 使用manual redirect模式来手动处理重定向
        const response = await fetch(currentUrl, {
          method: 'HEAD',
          signal: signal,
          redirect: 'manual', // 手动处理重定向
          cache: 'no-cache',
        });

        // 记录重定向信息
        if (response.status >= 300 && response.status < 400) {
          const location = response.headers.get('Location');
          if (location) {
            // 处理相对URL
            const nextUrl = new URL(location, currentUrl).href;
            redirectChain.push({
              from: currentUrl,
              to: nextUrl,
              status: response.status,
              statusText: response.statusText,
            });
            currentUrl = nextUrl;
            redirectCount++;
            continue;
          }
        }

        // 如果HEAD请求失败，尝试GET请求
        let finalResponse = response;
        if (!response.ok || response.status === 405) {
          finalResponse = await fetch(currentUrl, {
            method: 'GET',
            signal: signal,
            redirect: 'manual',
            cache: 'no-cache',
          });
        }

        return {
          originalUrl: originalUrl,
          finalUrl: currentUrl,
          redirectChain: redirectChain,
          response: finalResponse,
          redirectCount: redirectCount,
        };
      } catch (error) {
        // 如果手动模式失败，回退到自动模式
        const fallbackResponse = await fetch(currentUrl, {
          method: 'HEAD',
          signal: signal,
          redirect: 'follow',
          cache: 'no-cache',
        });

        return {
          originalUrl: originalUrl,
          finalUrl: fallbackResponse.url || currentUrl,
          redirectChain: redirectChain,
          response: fallbackResponse,
          redirectCount: redirectCount,
          fallback: true,
        };
      }
    }

    throw new Error(`Too many redirects (${maxRedirects})`);
  }

  async checkWithAlternativeFetch(url) {
    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      this.settings.timeout,
    );

    try {
      // 使用不同的 fetch 配置作为备选方案
      const response = await fetch(url, {
        method: 'GET',
        signal: controller.signal,
        mode: 'cors', // 尝试 CORS 模式
        cache: 'no-cache',
        redirect: 'follow',
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; BookmarkChecker/1.0)',
        },
      });

      clearTimeout(timeoutId);
      return this.analyzeResponse(response, 'alternative-fetch');
    } catch (error) {
      clearTimeout(timeoutId);

      // 如果 CORS 失败，尝试 no-cors 模式
      try {
        const noCorsController = new AbortController();
        const noCorsTimeoutId = setTimeout(
          () => noCorsController.abort(),
          this.settings.timeout,
        );

        const noCorsResponse = await fetch(url, {
          method: 'HEAD',
          signal: noCorsController.signal,
          mode: 'no-cors',
          cache: 'no-cache',
          redirect: 'follow',
        });

        clearTimeout(noCorsTimeoutId);

        // no-cors 模式下，成功的请求通常返回 status 0
        if (noCorsResponse.type === 'opaque') {
          return {
            status: 'valid',
            statusCode: 0,
            method: 'no-cors-fetch',
            note: 'Accessible via no-cors mode',
          };
        }

        return this.analyzeResponse(noCorsResponse, 'no-cors-fetch');
      } catch (noCorsError) {
        throw error; // 返回原始错误
      }
    }
  }

  async checkWithSimpleFetch(url) {
    // 最简单的 fetch 检测，作为最后的备选方案
    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      this.settings.timeout,
    );

    try {
      const response = await fetch(url, {
        method: 'GET',
        signal: controller.signal,
        mode: 'no-cors',
        cache: 'no-cache',
      });

      clearTimeout(timeoutId);

      // 对于 no-cors 模式，成功的请求通常返回 opaque 响应
      if (response.type === 'opaque' || response.ok) {
        return {
          status: 'valid',
          statusCode: response.status || 0,
          method: 'simple-fetch',
          note: 'Basic connectivity confirmed',
        };
      }

      return this.analyzeResponse(response, 'simple-fetch');
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  async checkWithImage(url) {
    // 图片检测需要在content script中执行，因为background没有Image对象
    return new Promise((resolve, reject) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs.length === 0) {
          reject(new Error('No active tab for image test'));
          return;
        }

        chrome.scripting.executeScript(
          {
            target: { tabId: tabs[0].id },
            func: this.imageTestInContentScript,
            args: [url, this.settings.timeout],
          },
          (results) => {
            if (chrome.runtime.lastError || !results || !results[0]) {
              reject(new Error('Image test failed to execute'));
            } else {
              const result = results[0].result;
              if (result.success) {
                resolve({
                  status: 'valid',
                  statusCode: 200,
                  method: 'image',
                  note: 'Loaded as image',
                });
              } else {
                reject(new Error(result.error));
              }
            }
          },
        );
      });
    });
  }

  async checkWithContentScript(url) {
    // 使用content script进行综合检测
    return new Promise((resolve, reject) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs.length === 0) {
          reject(new Error('No active tab for content script test'));
          return;
        }

        chrome.scripting.executeScript(
          {
            target: { tabId: tabs[0].id },
            func: this.comprehensiveTestInContentScript,
            args: [url, this.settings.timeout],
          },
          (results) => {
            if (chrome.runtime.lastError || !results || !results[0]) {
              reject(new Error('Content script test failed to execute'));
            } else {
              const result = results[0].result;
              if (result.success) {
                resolve({
                  status: 'valid',
                  statusCode: result.statusCode || 200,
                  method: result.method,
                  note: result.note,
                });
              } else {
                reject(new Error(result.error));
              }
            }
          },
        );
      });
    });
  }

  // 在content script中执行的综合测试函数
  comprehensiveTestInContentScript(url, timeout) {
    return new Promise((resolve) => {
      // 尝试多种方法
      const methods = [
        () => testWithFetch(url, timeout),
        () => testWithImage(url, timeout),
        () => testWithIframe(url, timeout),
      ];

      let currentMethodIndex = 0;

      const tryNextMethod = () => {
        if (currentMethodIndex >= methods.length) {
          resolve({
            success: false,
            error: 'All content script methods failed',
          });
          return;
        }

        const method = methods[currentMethodIndex];
        currentMethodIndex++;

        method()
          .then((result) => {
            resolve({
              success: true,
              statusCode: result.statusCode,
              method: result.method,
              note: result.note,
            });
          })
          .catch(() => {
            tryNextMethod();
          });
      };

      tryNextMethod();
    });

    // 内部方法：Fetch测试
    function testWithFetch(url, timeout) {
      return new Promise((resolve, reject) => {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        fetch(url, {
          method: 'HEAD',
          signal: controller.signal,
          mode: 'no-cors',
        })
          .then((response) => {
            clearTimeout(timeoutId);
            resolve({
              statusCode: response.status,
              method: 'content-fetch',
              note: 'Tested via content script fetch',
            });
          })
          .catch((error) => {
            clearTimeout(timeoutId);
            reject(error);
          });
      });
    }

    // 内部方法：图片测试
    function testWithImage(url, timeout) {
      return new Promise((resolve, reject) => {
        const img = new Image();
        const timeoutId = setTimeout(() => {
          img.onload = img.onerror = null;
          reject(new Error('Image load timeout'));
        }, timeout);

        img.onload = () => {
          clearTimeout(timeoutId);
          resolve({
            statusCode: 200,
            method: 'content-image',
            note: 'Loaded as image via content script',
          });
        };

        img.onerror = () => {
          clearTimeout(timeoutId);
          reject(new Error('Image load failed'));
        };

        img.src = url;
      });
    }

    // 内部方法：iframe测试
    function testWithIframe(url, timeout) {
      return new Promise((resolve, reject) => {
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.style.width = '1px';
        iframe.style.height = '1px';

        const timeoutId = setTimeout(() => {
          if (iframe.parentNode) {
            document.body.removeChild(iframe);
          }
          reject(new Error('Iframe load timeout'));
        }, timeout);

        iframe.onload = () => {
          clearTimeout(timeoutId);
          if (iframe.parentNode) {
            document.body.removeChild(iframe);
          }
          resolve({
            statusCode: 200,
            method: 'content-iframe',
            note: 'Loaded in iframe via content script',
          });
        };

        iframe.onerror = () => {
          clearTimeout(timeoutId);
          if (iframe.parentNode) {
            document.body.removeChild(iframe);
          }
          reject(new Error('Iframe load failed'));
        };

        document.body.appendChild(iframe);
        iframe.src = url;
      });
    }
  }

  // 在content script中执行的图片测试函数
  imageTestInContentScript(url, timeout) {
    return new Promise((resolve) => {
      const img = new Image();
      const timeoutId = setTimeout(() => {
        img.onload = img.onerror = null;
        resolve({
          success: false,
          error: 'Image load timeout',
        });
      }, timeout);

      img.onload = () => {
        clearTimeout(timeoutId);
        resolve({
          success: true,
        });
      };

      img.onerror = () => {
        clearTimeout(timeoutId);
        resolve({
          success: false,
          error: 'Not an image or image load failed',
        });
      };

      img.src = url;
    });
  }

  async checkWithIframe(url) {
    // iframe检测需要在content script中执行，因为background没有DOM访问权限
    return new Promise((resolve, reject) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs.length === 0) {
          reject(new Error('No active tab for iframe test'));
          return;
        }

        chrome.scripting.executeScript(
          {
            target: { tabId: tabs[0].id },
            func: this.iframeTestInContentScript,
            args: [url, this.settings.timeout],
          },
          (results) => {
            if (chrome.runtime.lastError || !results || !results[0]) {
              reject(new Error('Iframe test failed to execute'));
            } else {
              const result = results[0].result;
              if (result.success) {
                resolve({
                  status: 'valid',
                  statusCode: 200,
                  method: 'iframe',
                  note: 'Loaded in iframe',
                });
              } else {
                reject(new Error(result.error));
              }
            }
          },
        );
      });
    });
  }

  // 在content script中执行的iframe测试函数
  iframeTestInContentScript(url, timeout) {
    return new Promise((resolve) => {
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.style.width = '1px';
      iframe.style.height = '1px';

      const timeoutId = setTimeout(() => {
        if (iframe.parentNode) {
          document.body.removeChild(iframe);
        }
        resolve({
          success: false,
          error: 'Iframe load timeout',
        });
      }, timeout);

      iframe.onload = () => {
        clearTimeout(timeoutId);
        if (iframe.parentNode) {
          document.body.removeChild(iframe);
        }
        resolve({
          success: true,
        });
      };

      iframe.onerror = () => {
        clearTimeout(timeoutId);
        if (iframe.parentNode) {
          document.body.removeChild(iframe);
        }
        resolve({
          success: false,
          error: 'Iframe load failed',
        });
      };

      document.body.appendChild(iframe);
      iframe.src = url;
    });
  }

  analyzeResponse(response, method, redirectInfo = null) {
    const status = response.status;
    const statusText = response.statusText;

    // 定义成功的状态码范围
    const successCodes = [200, 201, 202, 203, 204, 205, 206];
    const redirectCodes = [300, 301, 302, 303, 304, 307, 308];
    const clientErrorCodes = [400, 401, 403, 404, 405, 406, 407, 408, 409, 410];
    const serverErrorCodes = [500, 501, 502, 503, 504, 505];

    // 基础结果对象
    const result = {
      statusCode: status,
      statusText: statusText,
      method: method,
    };

    // 添加重定向信息
    if (redirectInfo) {
      result.originalUrl = redirectInfo.originalUrl;
      result.finalUrl = redirectInfo.finalUrl;
      result.redirectCount = redirectInfo.redirectCount;
      result.redirectChain = redirectInfo.redirectChain;

      // 检查是否有重定向到不同域名
      if (redirectInfo.redirectCount > 0) {
        const originalDomain = this.extractDomain(redirectInfo.originalUrl);
        const finalDomain = this.extractDomain(redirectInfo.finalUrl);

        if (originalDomain !== finalDomain) {
          result.domainChanged = true;
          result.newDomain = finalDomain;
          result.note = `Redirected to different domain: ${finalDomain}`;
        } else {
          result.note = `Redirected ${redirectInfo.redirectCount} time(s)`;
        }
      }
    }

    if (successCodes.includes(status)) {
      return {
        ...result,
        status: 'valid',
      };
    }

    if (redirectCodes.includes(status)) {
      return {
        ...result,
        status: 'valid',
        note: result.note || 'Redirect response (considered valid)',
      };
    }

    if (clientErrorCodes.includes(status)) {
      // 某些客户端错误可能表示URL存在但有访问限制
      if ([401, 403, 405].includes(status)) {
        return {
          ...result,
          status: 'valid',
          note: 'Access restricted but URL exists',
        };
      }

      return {
        ...result,
        status: 'invalid',
        error: `Client error: ${status} ${statusText}`,
      };
    }

    if (serverErrorCodes.includes(status)) {
      return {
        ...result,
        status: 'warning',
        error: `Server error: ${status} ${statusText}`,
        note: 'Server error (URL may be temporarily unavailable)',
      };
    }

    // 其他状态码
    return {
      ...result,
      status: 'unknown',
      error: `Unknown status: ${status} ${statusText}`,
    };
  }

  extractDomain(url) {
    try {
      return new URL(url).hostname;
    } catch {
      return '';
    }
  }

  getRedirectSuggestions() {
    const suggestions = [];

    for (const [bookmarkId, result] of this.results.entries()) {
      if (result.domainChanged && result.finalUrl && result.originalUrl) {
        const bookmark = this.bookmarks?.find((b) => b.id === bookmarkId);
        if (bookmark) {
          suggestions.push({
            bookmarkId: bookmarkId,
            title: bookmark.title,
            originalUrl: result.originalUrl,
            finalUrl: result.finalUrl,
            newDomain: result.newDomain,
            redirectCount: result.redirectCount,
            redirectChain: result.redirectChain,
          });
        }
      }
    }

    return suggestions;
  }

  async makeRequest(url, method, signal) {
    // 使用content script来绕过CORS限制
    return new Promise((resolve, reject) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs.length === 0) {
          // 如果没有活动标签页，直接使用fetch
          fetch(url, { method, signal, mode: 'no-cors' })
            .then(resolve)
            .catch(reject);
          return;
        }

        // 尝试注入content script进行检测
        chrome.scripting.executeScript(
          {
            target: { tabId: tabs[0].id },
            func: this.checkUrlInContentScript,
            args: [url, method],
          },
          (results) => {
            if (chrome.runtime.lastError || !results || !results[0]) {
              // 如果注入失败，回退到普通fetch
              fetch(url, { method, signal, mode: 'no-cors' })
                .then(resolve)
                .catch(reject);
            } else {
              const result = results[0].result;
              if (result.success) {
                resolve({
                  ok: result.ok,
                  status: result.status,
                  statusText: result.statusText,
                });
              } else {
                reject(new Error(result.error));
              }
            }
          },
        );
      });
    });
  }

  checkUrlInContentScript(url, method) {
    return new Promise((resolve) => {
      fetch(url, { method })
        .then((response) => {
          resolve({
            success: true,
            ok: response.ok,
            status: response.status,
            statusText: response.statusText,
          });
        })
        .catch((error) => {
          resolve({
            success: false,
            error: error.message,
          });
        });
    });
  }

  getErrorMessage(error) {
    if (error.name === 'AbortError') {
      return 'Request timeout';
    }
    if (
      error.name === 'TypeError' &&
      error.message.includes('Failed to fetch')
    ) {
      return 'Network error or CORS blocked';
    }
    if (error.message && error.message.includes('Content script')) {
      return 'Content script unavailable, using basic detection';
    }
    if (error.message && error.message.includes('No active tab')) {
      return 'No active tab available for enhanced detection';
    }
    return error.message || 'Unknown error';
  }

  async showCompletionNotification(totalBookmarks) {
    const settings = await chrome.storage.local.get(['settings']);
    if (!settings.settings?.showNotifications) {
      return;
    }

    const validCount = Array.from(this.results.values()).filter(
      (r) => r.status === 'valid',
    ).length;
    const invalidCount = Array.from(this.results.values()).filter(
      (r) => r.status === 'invalid' || r.status === 'error',
    ).length;

    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: '书签检测完成',
      message: `检测了 ${totalBookmarks} 个书签\n有效: ${validCount}, 失效: ${invalidCount}`,
    });
  }

  notifyPopup(type, data = null) {
    // 尝试向popup发送消息
    chrome.runtime
      .sendMessage({
        type: type,
        data: data,
      })
      .catch(() => {
        // popup可能未打开，忽略错误
      });
  }
}

// 初始化书签检测器
new BookmarkChecker();
