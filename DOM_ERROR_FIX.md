# 🔧 修复 "document is not defined" 错误

## 问题分析

### 错误原因
```
ReferenceError: document is not defined
```

这个错误发生的原因是：
1. **环境差异**: Background script 运行在 Service Worker 环境中，没有 DOM 访问权限
2. **API限制**: Service Worker 无法访问 `document`、`window`、`Image` 等浏览器 API
3. **架构问题**: 在 background.js 中直接使用了需要 DOM 的代码

### Chrome扩展环境对比

| 环境 | DOM访问 | 可用API | 用途 |
|------|---------|---------|------|
| **Background Script** | ❌ 无 | Chrome APIs | 后台逻辑、数据处理 |
| **Content Script** | ✅ 有 | DOM + 部分Chrome APIs | 页面交互、DOM操作 |
| **Popup Script** | ✅ 有 | DOM + Chrome APIs | 用户界面 |

## 🔧 解决方案

### 1. 架构重构
将需要 DOM 的操作从 background script 移到 content script 中执行：

```javascript
// ❌ 错误：在 background.js 中使用 DOM
async checkWithImage(url) {
  const img = new Image(); // ReferenceError: Image is not defined
  img.src = url;
}

// ✅ 正确：通过 content script 执行
async checkWithImage(url) {
  return new Promise((resolve, reject) => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      chrome.scripting.executeScript({
        target: { tabId: tabs[0].id },
        func: this.imageTestInContentScript,
        args: [url, this.settings.timeout]
      }, (results) => {
        // 处理结果
      });
    });
  });
}
```

### 2. Content Script 注入
使用 `chrome.scripting.executeScript` 在页面中执行需要 DOM 的代码：

```javascript
// 在 background.js 中
chrome.scripting.executeScript({
  target: { tabId: tabId },
  func: functionToExecute,
  args: [param1, param2]
}, (results) => {
  // 处理执行结果
});

// 要执行的函数（会在页面环境中运行）
function functionToExecute(param1, param2) {
  // 这里可以使用 document、Image 等 DOM API
  const img = new Image();
  // ...
}
```

### 3. 检测方法优化

#### 原有问题方法
```javascript
// ❌ 在 background 中直接使用 DOM
async checkWithImage(url) {
  const img = new Image(); // 错误！
  img.src = url;
}

async checkWithIframe(url) {
  const iframe = document.createElement('iframe'); // 错误！
  document.body.appendChild(iframe);
}
```

#### 修复后的方法
```javascript
// ✅ 通过 content script 执行
async checkWithContentScript(url) {
  return new Promise((resolve, reject) => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs.length === 0) {
        reject(new Error('No active tab'));
        return;
      }

      chrome.scripting.executeScript({
        target: { tabId: tabs[0].id },
        func: this.comprehensiveTestInContentScript,
        args: [url, this.settings.timeout]
      }, (results) => {
        if (chrome.runtime.lastError || !results || !results[0]) {
          reject(new Error('Content script execution failed'));
        } else {
          const result = results[0].result;
          if (result.success) {
            resolve({
              status: 'valid',
              statusCode: result.statusCode,
              method: result.method,
              note: result.note
            });
          } else {
            reject(new Error(result.error));
          }
        }
      });
    });
  });
}
```

### 4. 综合检测策略

#### 新的检测流程
```
1. Fetch API 检测 (background) → 
2. Content Script 检测 (页面环境) →
3. 结果分析和返回
```

#### Content Script 中的多重检测
```javascript
comprehensiveTestInContentScript(url, timeout) {
  return new Promise((resolve) => {
    const methods = [
      () => testWithFetch(url, timeout),    // 页面环境的 fetch
      () => testWithImage(url, timeout),    // Image 对象测试
      () => testWithIframe(url, timeout)    // iframe 加载测试
    ];

    // 依次尝试各种方法
    let currentMethodIndex = 0;
    const tryNextMethod = () => {
      if (currentMethodIndex >= methods.length) {
        resolve({ success: false, error: 'All methods failed' });
        return;
      }

      const method = methods[currentMethodIndex];
      currentMethodIndex++;

      method()
        .then(result => resolve({ success: true, ...result }))
        .catch(() => tryNextMethod());
    };

    tryNextMethod();
  });
}
```

## 🎯 修复效果

### 修复前
```
❌ ReferenceError: document is not defined
❌ ReferenceError: Image is not defined  
❌ 检测功能完全失效
❌ 扩展无法正常工作
```

### 修复后
```
✅ 所有 DOM 操作通过 content script 执行
✅ 多重检测方法正常工作
✅ 图片和 iframe 检测恢复功能
✅ 扩展完全正常运行
```

## 📋 最佳实践

### 1. 环境分离原则
- **Background Script**: 只处理数据逻辑、API调用、存储操作
- **Content Script**: 处理所有需要 DOM 的操作
- **Popup Script**: 处理用户界面交互

### 2. 错误处理
```javascript
// 检查 content script 执行结果
if (chrome.runtime.lastError) {
  console.error('Content script error:', chrome.runtime.lastError);
  // 降级处理
}

// 检查活动标签页
chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
  if (tabs.length === 0) {
    // 没有活动标签页的处理
    fallbackMethod();
    return;
  }
  // 正常执行
});
```

### 3. 降级策略
```javascript
// 如果 content script 失败，使用纯 fetch 方法
async checkWithFallback(url) {
  try {
    return await this.checkWithContentScript(url);
  } catch (error) {
    console.warn('Content script failed, using fetch fallback:', error);
    return await this.checkWithFetch(url);
  }
}
```

## 🔍 调试技巧

### 1. 检查执行环境
```javascript
// 在代码中添加环境检查
console.log('Environment check:');
console.log('- document available:', typeof document !== 'undefined');
console.log('- Image available:', typeof Image !== 'undefined');
console.log('- chrome.scripting available:', !!chrome.scripting);
```

### 2. Content Script 调试
```javascript
// 在 content script 函数中添加日志
function testFunction() {
  console.log('Content script executing in:', window.location.href);
  console.log('DOM available:', !!document);
  // 执行实际逻辑
}
```

### 3. 错误监控
```javascript
// 添加全局错误处理
chrome.scripting.executeScript({
  target: { tabId: tabId },
  func: testFunction
}, (results) => {
  if (chrome.runtime.lastError) {
    console.error('Execution error:', chrome.runtime.lastError.message);
  }
  if (results && results[0] && results[0].error) {
    console.error('Script error:', results[0].error);
  }
});
```

## 🚀 验证修复

### 测试步骤
1. **重新加载扩展**: 确保新代码生效
2. **打开开发者工具**: 检查 background 页面的 console
3. **运行检测**: 点击"开始扫描"按钮
4. **观察结果**: 确认没有 DOM 相关错误

### 成功标志
- ✅ Console 中没有 "document is not defined" 错误
- ✅ 书签检测功能正常工作
- ✅ 所有检测方法都能执行
- ✅ 重定向跟踪正常运行

这个修复确保了扩展在 Manifest V3 环境下的稳定运行！🎉
