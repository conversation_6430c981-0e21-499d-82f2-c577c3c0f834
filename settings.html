<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>书签检测器 - 设置</title>
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
    />
    <link rel="stylesheet" href="styles/settings.css" />
  </head>
  <body>
    <div class="container">
      <header class="header">
        <h1 class="title">设置</h1>
        <button id="backBtn" class="btn btn-secondary">
          <span class="icon">←</span>
          返回
        </button>
      </header>

      <div class="settings-content">
        <!-- 定时检测设置 -->
        <div class="setting-group">
          <h3 class="group-title">
            <span class="material-icons">schedule</span>
            定时自动检测
          </h3>

          <div class="setting-item">
            <label for="enableScheduled" class="setting-label">
              启用定时检测
              <span class="setting-description">自动定期检测书签的有效性</span>
            </label>
            <input
              type="checkbox"
              id="enableScheduled"
              class="setting-checkbox"
            />
          </div>

          <div class="setting-item" id="scheduledOptions">
            <label for="checkInterval" class="setting-label">
              检测频率
              <span class="setting-description">定时检测的时间间隔</span>
            </label>
            <select id="checkInterval" class="setting-select">
              <option value="daily">每日</option>
              <option value="weekly" selected>每周</option>
              <option value="monthly">每月</option>
              <option value="custom">自定义</option>
            </select>
          </div>

          <div
            class="setting-item"
            id="customIntervalGroup"
            style="display: none"
          >
            <label for="customMinutes" class="setting-label">
              自定义间隔（分钟）
              <span class="setting-description">范围：1分钟 - 30天</span>
            </label>
            <input
              type="number"
              id="customMinutes"
              class="setting-input"
              min="1"
              max="43200"
              value="60"
            />
          </div>

          <div class="setting-item">
            <label for="onlyInvalid" class="setting-label">
              只检测失效的书签
              <span class="setting-description"
                >仅重新检测之前标记为失效的书签</span
              >
            </label>
            <input type="checkbox" id="onlyInvalid" class="setting-checkbox" />
          </div>

          <div class="setting-item">
            <div class="status-info" id="scheduleStatus">
              <h4>定时检测状态</h4>
              <div class="status-item">
                <span class="label">状态:</span>
                <span class="value" id="statusText">未启用</span>
              </div>
              <div class="status-item">
                <span class="label">上次运行:</span>
                <span class="value" id="lastRunText">从未</span>
              </div>
              <div class="status-item">
                <span class="label">下次运行:</span>
                <span class="value" id="nextRunText">-</span>
              </div>
            </div>
          </div>

          <div class="setting-item">
            <button id="runNowBtn" class="btn btn-secondary">
              <span class="material-icons">play_arrow</span>
              立即运行一次
            </button>
          </div>
        </div>

        <div class="setting-group">
          <h3 class="group-title">
            <span class="material-icons">tune</span>
            检测设置
          </h3>

          <div class="setting-item">
            <label for="timeoutSetting" class="setting-label">
              请求超时时间 (秒)
              <span class="setting-description"
                >每个书签检测的最大等待时间</span
              >
            </label>
            <input
              type="number"
              id="timeoutSetting"
              class="setting-input"
              min="1"
              max="60"
              value="10"
            />
          </div>

          <div class="setting-item">
            <label for="concurrentSetting" class="setting-label">
              并发检测数量
              <span class="setting-description"
                >同时检测的书签数量，数值越大速度越快但可能影响稳定性</span
              >
            </label>
            <input
              type="number"
              id="concurrentSetting"
              class="setting-input"
              min="1"
              max="20"
              value="5"
            />
          </div>

          <div class="setting-item">
            <label for="retriesSetting" class="setting-label">
              重试次数
              <span class="setting-description">检测失败时的重试次数</span>
            </label>
            <input
              type="number"
              id="retriesSetting"
              class="setting-input"
              min="0"
              max="5"
              value="2"
            />
          </div>
        </div>

        <div class="setting-group">
          <h3 class="group-title">界面设置</h3>

          <div class="setting-item">
            <label for="autoScanSetting" class="setting-label">
              启动时自动扫描
              <span class="setting-description"
                >打开扩展时自动开始检测书签</span
              >
            </label>
            <input
              type="checkbox"
              id="autoScanSetting"
              class="setting-checkbox"
            />
          </div>

          <div class="setting-item">
            <label for="showNotificationsSetting" class="setting-label">
              显示通知
              <span class="setting-description">检测完成后显示系统通知</span>
            </label>
            <input
              type="checkbox"
              id="showNotificationsSetting"
              class="setting-checkbox"
            />
          </div>

          <div class="setting-item">
            <label for="darkModeSetting" class="setting-label">
              深色模式
              <span class="setting-description">使用深色主题</span>
            </label>
            <input
              type="checkbox"
              id="darkModeSetting"
              class="setting-checkbox"
            />
          </div>
        </div>

        <div class="setting-group">
          <h3 class="group-title">数据管理</h3>

          <div class="setting-item">
            <label for="cacheResultsSetting" class="setting-label">
              缓存检测结果
              <span class="setting-description"
                >保存检测结果以避免重复检测</span
              >
            </label>
            <input
              type="checkbox"
              id="cacheResultsSetting"
              class="setting-checkbox"
              checked
            />
          </div>

          <div class="setting-item">
            <label for="cacheExpiryHours" class="setting-label">
              缓存有效期 (小时)
              <span class="setting-description">检测结果的有效时间</span>
            </label>
            <input
              type="number"
              id="cacheExpiryHours"
              class="setting-input"
              min="1"
              max="168"
              value="24"
            />
          </div>
        </div>

        <div class="setting-group">
          <h3 class="group-title">高级设置</h3>

          <div class="setting-item">
            <label for="userAgentSetting" class="setting-label">
              自定义User-Agent
              <span class="setting-description"
                >用于检测请求的User-Agent字符串</span
              >
            </label>
            <input
              type="text"
              id="userAgentSetting"
              class="setting-input"
              placeholder="留空使用默认"
            />
          </div>

          <div class="setting-item">
            <label for="excludePatternsSetting" class="setting-label">
              排除模式
              <span class="setting-description"
                >不检测匹配这些模式的URL (每行一个)</span
              >
            </label>
            <textarea
              id="excludePatternsSetting"
              class="setting-textarea"
              rows="3"
              placeholder="例如: localhost&#10;127.0.0.1&#10;*.local"
            ></textarea>
          </div>
        </div>
      </div>

      <div class="actions">
        <button id="saveBtn" class="btn btn-primary">保存设置</button>
        <button id="resetBtn" class="btn btn-secondary">重置为默认</button>
        <button id="exportSettingsBtn" class="btn btn-secondary">
          导出设置
        </button>
        <button id="importSettingsBtn" class="btn btn-secondary">
          导入设置
        </button>
      </div>

      <input
        type="file"
        id="importFileInput"
        accept=".json"
        style="display: none"
      />
    </div>

    <script src="scripts/settings.js"></script>
  </body>
</html>
