{"name": "aipan-chrome-tags-panel", "version": "1.0.0", "description": "智能Chrome书签检测和管理扩展", "main": "background.js", "scripts": {"lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "build": "echo 'Building extension...' && npm run lint", "dev": "npm run format && npm run lint:fix", "test": "echo 'No tests specified yet'"}, "keywords": ["chrome-extension", "bookmarks", "link-checker", "bookmark-manager"], "author": "Your Name", "license": "MIT", "devDependencies": {"eslint": "^8.0.0", "prettier": "^3.6.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/aipan-chrome-tags-panel.git"}, "bugs": {"url": "https://github.com/yourusername/aipan-chrome-tags-panel/issues"}, "homepage": "https://github.com/yourusername/aipan-chrome-tags-panel#readme"}