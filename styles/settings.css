* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 500px;
  min-height: 600px;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  background: #f8f9fa;
  color: #333;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.header {
  background: #fff;
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.settings-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.setting-group {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.group-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e9ecef;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: block;
  font-weight: 500;
  color: #495057;
  margin-bottom: 6px;
  cursor: pointer;
}

.setting-description {
  display: block;
  font-size: 12px;
  color: #6c757d;
  font-weight: normal;
  margin-top: 2px;
}

.setting-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.setting-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.setting-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease;
}

.setting-textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.setting-checkbox {
  width: auto;
  margin-right: 8px;
  transform: scale(1.2);
}

.actions {
  padding: 20px;
  background: #fff;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border: 1px solid #c3e6cb;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border: 1px solid #f5c6cb;
}

.setting-item.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.setting-input[type='number'] {
  width: 120px;
}

.setting-range {
  width: 100%;
  margin: 8px 0;
}

.range-value {
  font-size: 12px;
  color: #6c757d;
  text-align: center;
  margin-top: 4px;
}

/* 深色模式样式 */
body.dark-mode {
  background: #1a1a1a;
  color: #e9ecef;
}

.dark-mode .header,
.dark-mode .setting-group,
.dark-mode .actions {
  background: #2d3748;
  border-color: #4a5568;
}

.dark-mode .title,
.dark-mode .group-title {
  color: #e9ecef;
}

.dark-mode .setting-label {
  color: #cbd5e0;
}

.dark-mode .setting-description {
  color: #a0aec0;
}

.dark-mode .setting-input,
.dark-mode .setting-textarea {
  background: #4a5568;
  border-color: #718096;
  color: #e9ecef;
}

.dark-mode .setting-input:focus,
.dark-mode .setting-textarea:focus {
  border-color: #63b3ed;
  box-shadow: 0 0 0 2px rgba(99, 179, 237, 0.25);
}

.fade-in {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
