/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Material Icons 样式 */
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 16px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
  vertical-align: middle;
}

body {
  width: 400px;
  min-height: 500px;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  background: #f8f9fa;
  color: #333;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.header {
  background: #fff;
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #2c3e50;
}

.controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.scan-controls {
  display: flex;
  gap: 4px;
}

.btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.btn .material-icons {
  font-size: 18px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-outline {
  background: transparent;
  color: #007bff;
  border: 1px solid #007bff;
}

.btn-outline:hover {
  background: #007bff;
  color: white;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #e9ecef;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.stat-label {
  color: #6c757d;
}

.stat-value {
  font-weight: 600;
}

.stat-value.valid {
  color: #28a745;
}

.stat-value.invalid {
  color: #dc3545;
}

.stat-value.checking {
  color: #ffc107;
}

.filters {
  display: flex;
  padding: 12px 16px;
  gap: 4px;
  background: #fff;
  border-bottom: 1px solid #e9ecef;
}

.filter-btn {
  padding: 6px 12px;
  border: 1px solid #dee2e6;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: #f8f9fa;
}

.filter-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.filter-btn .count {
  font-size: 11px;
  opacity: 0.8;
  margin-left: 4px;
}

.filter-btn.active .count {
  opacity: 1;
}

.search-box {
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #e9ecef;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 12px;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.bookmark-list {
  flex: 1;
  overflow-y: auto;
  max-height: 300px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6c757d;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 增量检测信息提示 */
.incremental-info {
  background: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 6px;
  margin: 8px 0;
  animation: slideIn 0.3s ease;
}

.incremental-info .info-content {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  gap: 8px;
  font-size: 12px;
  color: #1976d2;
}

.incremental-info .material-icons {
  font-size: 16px;
  color: #2196f3;
}

.incremental-info .close-info {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 3px;
  margin-left: auto;
  color: #1976d2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.incremental-info .close-info:hover {
  background: rgba(33, 150, 243, 0.1);
}

.incremental-info .close-info .material-icons {
  font-size: 14px;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 导出弹窗样式 */
.export-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
  max-height: 80%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.close-modal {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-modal:hover {
  background: #e9ecef;
  color: #495057;
}

.close-modal .material-icons {
  font-size: 20px;
}

.modal-body {
  padding: 20px;
}

.modal-body p {
  margin: 0 0 16px 0;
  color: #495057;
  font-size: 14px;
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.export-option-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.export-option-btn:hover {
  border-color: #007bff;
  background: #f8f9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.export-option-btn .material-icons {
  font-size: 24px;
  color: #007bff;
  flex-shrink: 0;
}

.export-option-btn span:not(.material-icons) {
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
}

.export-option-btn small {
  display: block;
  color: #6c757d;
  font-size: 12px;
  margin-top: 2px;
}

/* 消息提示样式 */
.message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 12px;
  font-size: 14px;
  animation: slideIn 0.3s ease;
}

.message-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message-error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.message-info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.message .material-icons {
  font-size: 18px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.bookmark-item {
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  background: #fff;
  transition:
    all 0.3s ease,
    background-color 0.2s ease,
    border-left 0.3s ease;
}

.bookmark-item:hover {
  background: #f8f9fa;
}

.bookmark-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.bookmark-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
  transition: all 0.3s ease;
  position: relative;
}

.bookmark-status.valid {
  background: #28a745;
}

.bookmark-status.invalid {
  background: #dc3545;
}

.bookmark-status.error {
  background: #dc3545;
}

.bookmark-status.warning {
  background: #fd7e14;
}

.bookmark-status.unknown {
  background: #6c757d;
}

.bookmark-status.checking {
  background: #ffc107;
  animation: pulse 1.5s ease-in-out infinite;
}

.bookmark-status.unchecked {
  background: #dee2e6;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.bookmark-title {
  font-weight: 500;
  color: #2c3e50;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bookmark-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  padding: 4px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 3px;
  color: #6c757d;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-width: 24px;
  min-height: 24px;
}

.action-btn .material-icons {
  font-size: 16px;
  pointer-events: none; /* 防止图标阻止点击事件 */
}

.action-btn:hover {
  background: #e9ecef;
  color: #495057;
  transform: scale(1.05);
}

.action-btn:active {
  transform: scale(0.95);
}

.bookmark-url {
  font-size: 11px;
  color: #6c757d;
  margin-left: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bookmark-error {
  font-size: 11px;
  color: #dc3545;
  margin-left: 20px;
  margin-top: 2px;
}

.batch-actions {
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
}

.batch-info {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 8px;
}

.batch-buttons {
  display: flex;
  gap: 6px;
}

.batch-buttons .btn {
  font-size: 11px;
  padding: 6px 10px;
}

.bookmark-checkbox {
  margin-right: 8px;
  cursor: pointer;
}

.bookmark-item.selected {
  background: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.actions {
  padding: 16px;
  background: #fff;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6c757d;
  text-align: center;
}

.empty-state .icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.progress-bar {
  margin-top: 12px;
  padding: 8px 0;
}

.progress-track {
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  border-radius: 3px;
  transition: width 0.3s ease;
  width: 0%;
}

.progress-text {
  font-size: 11px;
  color: #6c757d;
  text-align: center;
}

.bookmark-item.checking {
  background: #fff3cd;
  border-left: 3px solid #ffc107;
}

.bookmark-item.valid {
  border-left: 3px solid #28a745;
}

.bookmark-item.invalid {
  border-left: 3px solid #dc3545;
}

.bookmark-item.timeout {
  border-left: 3px solid #fd7e14;
}

.action-btn:hover {
  transform: scale(1.1);
}

.stats {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bookmark-item {
  animation: slideIn 0.2s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.message {
  padding: 12px 16px;
  margin: 0;
  font-size: 13px;
  font-weight: 500;
  border-radius: 0;
  animation: slideDown 0.3s ease;
}

.message-success {
  background: #d4edda;
  color: #155724;
  border-bottom: 2px solid #28a745;
}

.message-error {
  background: #f8d7da;
  color: #721c24;
  border-bottom: 2px solid #dc3545;
}

.message-info {
  background: #d1ecf1;
  color: #0c5460;
  border-bottom: 2px solid #17a2b8;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.hidden {
  display: none !important;
}

/* 错误状态样式 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.error-state .icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #ff6b6b;
}

.error-state .message {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.error-state .description {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.error-state .btn {
  margin: 4px;
  min-width: 120px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.empty-state .icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state div:last-child {
  font-size: 14px;
  color: #999;
}

/* 重定向建议样式 */
.redirect-suggestions {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin: 16px 0;
  padding: 16px;
  animation: slideIn 0.3s ease;
}

.redirect-header h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #856404;
  display: flex;
  align-items: center;
  gap: 8px;
}

.redirect-header p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #856404;
}

.redirect-list {
  margin-bottom: 16px;
}

.redirect-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 8px;
}

.redirect-item:last-child {
  margin-bottom: 0;
}

.redirect-info {
  flex: 1;
}

.bookmark-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  font-size: 14px;
}

.redirect-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.url-change {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.old-domain {
  color: #dc3545;
  background: #f8d7da;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}

.new-domain {
  color: #28a745;
  background: #d4edda;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}

.arrow {
  color: #6c757d;
  font-weight: bold;
}

.redirect-count {
  font-size: 11px;
  color: #6c757d;
}

.redirect-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.redirect-actions .btn {
  display: flex;
  align-items: center;
  gap: 4px;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
  min-width: auto;
}

.redirect-actions.redirect-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
  justify-content: center;
}
