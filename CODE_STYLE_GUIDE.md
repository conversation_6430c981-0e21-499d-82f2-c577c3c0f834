# 代码风格配置指南

## 🎯 目标

确保 ESLint、Prettier 和 IDE 的代码格式化保持完全一致，避免格式冲突。

## 📁 配置文件说明

### 1. `.editorconfig`

- **作用**: 跨编辑器的基础配置
- **设置**: 缩进、换行符、字符编码等
- **支持**: 大多数现代编辑器都支持

### 2. `.prettierrc.js`

- **作用**: Prettier 代码格式化配置
- **设置**: 引号、分号、缩进、换行等
- **优先级**: 高于编辑器默认设置

### 3. `.eslintrc.js`

- **作用**: ESLint 代码检查和格式化
- **设置**: 代码质量规则 + 风格规则
- **与 Prettier 协调**: 避免冲突规则

### 4. `.vscode/settings.json`

- **作用**: VSCode 工作区特定设置
- **设置**: 自动格式化、保存时修复等
- **仅影响**: 当前项目

## 🔧 统一的代码风格

### 基础规则

- **缩进**: 2个空格
- **引号**: 单引号
- **分号**: 必须有
- **换行符**: LF (Unix风格)
- **尾随逗号**: ES5兼容
- **行尾**: 必须有换行符

### JavaScript 特定规则

```javascript
// ✅ 正确
const obj = { name: 'test', value: 42 };
const arr = [1, 2, 3];

if (condition) {
  doSomething();
}

// ❌ 错误
const obj = { name: 'test', value: 42 };
const arr = [1, 2, 3];

if (condition) doSomething();
```

## 🚀 使用方法

### 开发时自动格式化

1. **保存时自动格式化**: VSCode 会自动运行 Prettier + ESLint
2. **手动格式化**: `Shift + Alt + F` (Windows/Linux) 或 `Shift + Option + F` (Mac)

### 命令行工具

```bash
# 检查代码风格
npm run lint

# 自动修复 ESLint 问题
npm run lint:fix

# 格式化所有文件
npm run format

# 检查格式是否正确
npm run format:check

# 开发模式 (格式化 + 修复)
npm run dev
```

### 构建前检查

```bash
# 构建项目 (包含代码检查)
npm run build
```

## 🔄 工作流程

### 推荐的开发流程

1. **编写代码**: 正常编写，不用担心格式
2. **保存文件**: VSCode 自动格式化
3. **提交前**: 运行 `npm run dev` 确保一致性
4. **构建**: 运行 `npm run build` 最终检查

### 团队协作

1. **统一工具**: 确保团队都使用相同的配置
2. **Git hooks**: 可以添加 pre-commit 钩子自动格式化
3. **CI/CD**: 在持续集成中检查代码风格

## 🛠️ IDE 设置

### VSCode 推荐扩展

- **ESLint**: 代码检查
- **Prettier**: 代码格式化
- **EditorConfig**: 基础编辑器配置

### 其他编辑器

- **WebStorm**: 内置支持 ESLint 和 Prettier
- **Sublime Text**: 需要安装相应插件
- **Vim/Neovim**: 通过插件支持

## 🐛 常见问题

### 格式化冲突

**问题**: ESLint 和 Prettier 规则冲突
**解决**: 我们的配置已经协调了两者，避免冲突

### IDE 不自动格式化

**检查**:

1. 是否安装了 Prettier 扩展
2. 是否设置了正确的默认格式化器
3. 是否启用了 "Format on Save"

### 配置不生效

**解决**:

1. 重启 IDE
2. 检查配置文件语法
3. 确认文件路径正确

## 📋 配置验证

### 测试配置是否正确

1. 创建一个测试文件:

```javascript
// test.js
const obj = { name: 'test', value: 42 };
if (true) console.log('hello');
```

2. 保存文件，应该自动格式化为:

```javascript
// test.js
const obj = { name: 'test', value: 42 };
if (true) {
  console.log('hello');
}
```

3. 运行 `npm run lint`，应该没有错误

## 🎉 优势

### 开发体验

- ✅ 自动格式化，无需手动调整
- ✅ 实时错误提示
- ✅ 团队代码风格统一
- ✅ 减少代码审查中的格式争议

### 代码质量

- ✅ 一致的代码风格
- ✅ 自动发现潜在问题
- ✅ 更好的可读性
- ✅ 减少 bug 风险

现在您的项目已经配置了完整的代码风格管理系统，ESLint、Prettier 和 IDE 将完美协作！
