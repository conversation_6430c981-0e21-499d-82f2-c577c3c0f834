// 简单的图标生成脚本
// 由于我们无法在这个环境中运行图像转换工具，
// 我们将创建一个简单的HTML文件来手动生成图标

const fs = require('fs');

const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>图标生成器</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-container { margin: 20px 0; }
        canvas { border: 1px solid #ccc; margin: 10px; }
    </style>
</head>
<body>
    <h1>Chrome扩展图标生成器</h1>
    <p>请右键点击每个图标并保存为对应的文件名：</p>
    
    <div class="icon-container">
        <h3>16x16 (保存为 icon16.png)</h3>
        <canvas id="icon16" width="16" height="16"></canvas>
    </div>
    
    <div class="icon-container">
        <h3>32x32 (保存为 icon32.png)</h3>
        <canvas id="icon32" width="32" height="32"></canvas>
    </div>
    
    <div class="icon-container">
        <h3>48x48 (保存为 icon48.png)</h3>
        <canvas id="icon48" width="48" height="48"></canvas>
    </div>
    
    <div class="icon-container">
        <h3>128x128 (保存为 icon128.png)</h3>
        <canvas id="icon128" width="128" height="128"></canvas>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 128;
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 背景圆形
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#007bff');
            gradient.addColorStop(1, '#0056b3');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 4*scale, 0, 2 * Math.PI);
            ctx.fill();
            
            // 白色边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 4 * scale;
            ctx.stroke();
            
            // 书签形状
            ctx.fillStyle = '#fff';
            ctx.strokeStyle = '#e9ecef';
            ctx.lineWidth = 1 * scale;
            
            const bookmarkWidth = 48 * scale;
            const bookmarkHeight = 60 * scale;
            const bookmarkX = (size - bookmarkWidth) / 2;
            const bookmarkY = 30 * scale;
            
            ctx.beginPath();
            ctx.roundRect(bookmarkX, bookmarkY, bookmarkWidth, bookmarkHeight, 8 * scale);
            ctx.fill();
            ctx.stroke();
            
            // 书签底部的锯齿
            ctx.fillStyle = '#fff';
            ctx.beginPath();
            ctx.moveTo(bookmarkX + bookmarkWidth/4, bookmarkY + bookmarkHeight);
            ctx.lineTo(bookmarkX + bookmarkWidth/2, bookmarkY + bookmarkHeight - 12*scale);
            ctx.lineTo(bookmarkX + 3*bookmarkWidth/4, bookmarkY + bookmarkHeight);
            ctx.fill();
            
            // 检查标记
            ctx.strokeStyle = '#28a745';
            ctx.lineWidth = 4 * scale;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            ctx.beginPath();
            ctx.moveTo(bookmarkX + 10*scale, bookmarkY + 25*scale);
            ctx.lineTo(bookmarkX + 18*scale, bookmarkY + 33*scale);
            ctx.lineTo(bookmarkX + 38*scale, bookmarkY + 13*scale);
            ctx.stroke();
            
            // 搜索放大镜
            ctx.strokeStyle = '#ffc107';
            ctx.lineWidth = 3 * scale;
            
            // 圆形
            ctx.beginPath();
            ctx.arc(bookmarkX + 35*scale, bookmarkY + 15*scale, 8*scale, 0, 2 * Math.PI);
            ctx.stroke();
            
            // 手柄
            ctx.beginPath();
            ctx.moveTo(bookmarkX + 41*scale, bookmarkY + 21*scale);
            ctx.lineTo(bookmarkX + 47*scale, bookmarkY + 27*scale);
            ctx.stroke();
        }
        
        // 生成所有尺寸的图标
        ['16', '32', '48', '128'].forEach(size => {
            const canvas = document.getElementById('icon' + size);
            drawIcon(canvas, parseInt(size));
        });
    </script>
</body>
</html>
`;

fs.writeFileSync('icon-generator.html', htmlContent);
console.log('图标生成器已创建: icon-generator.html');
console.log('请在浏览器中打开此文件，然后右键保存每个图标到 icons/ 目录');
