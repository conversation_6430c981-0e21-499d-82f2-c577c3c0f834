module.exports = {
    env: {
        browser: true,
        es2021: true,
        webextensions: true,
        node: true,
    },
    extends: ['eslint:recommended'],
    parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'script',
    },
    globals: {
        chrome: 'readonly',
    },
    rules: {
        indent: ['error', 4],
        'linebreak-style': ['error', 'unix'],
        quotes: ['error', 'single'],
        semi: ['error', 'always'],
        'no-unused-vars': ['warn'],
        'no-console': ['warn'],
        'no-debugger': ['error'],
        'no-case-declarations': 'off',
    },
};
